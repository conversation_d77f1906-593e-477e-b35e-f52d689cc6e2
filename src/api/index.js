// API服务层 - 连接前端和后端
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // 通用请求方法
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }
      
      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // GET请求
  async get(endpoint) {
    return this.request(endpoint, { method: 'GET' });
  }

  // POST请求
  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // PUT请求
  async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // DELETE请求
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  // 文件上传
  async uploadFile(endpoint, file, additionalData = {}) {
    const formData = new FormData();
    formData.append('asset', file);
    
    // 添加其他数据
    Object.keys(additionalData).forEach(key => {
      formData.append(key, additionalData[key]);
    });

    return this.request(endpoint, {
      method: 'POST',
      body: formData,
      headers: {}, // 让浏览器自动设置Content-Type
    });
  }

  // === 项目管理 ===
  createProject = async (projectData) => {
    return this.post('/api/projects', projectData);
  }

  getAllProjects = async () => {
    return this.get('/api/projects');
  }

  getProject = async (projectId) => {
    return this.get(`/api/projects/${projectId}`);
  }

  updateProject = async (projectId, projectData) => {
    return this.put(`/api/projects/${projectId}`, projectData);
  }

  deleteProject = async (projectId) => {
    return this.delete(`/api/projects/${projectId}`);
  }

  uploadAsset = async (projectId, file) => {
    return this.uploadFile(`/api/projects/${projectId}/assets`, file);
  }

  // === 脚本生成 ===
  generateScripts = async (scriptData) => {
    return this.post('/api/scripts/generate', scriptData);
  }

  regenerateScript = async (scriptId, context) => {
    return this.post('/api/scripts/regenerate', { scriptId, context });
  }

  updateScript = async (scriptId, scriptData) => {
    return this.put(`/api/scripts/${scriptId}`, scriptData);
  }

  deleteScript = async (scriptId) => {
    return this.delete(`/api/scripts/${scriptId}`);
  }

  generateShots = async (scripts) => {
    return this.post('/api/scripts/shots/generate', { scripts });
  }

  async regenerateShot(shotId, scriptText) {
    return this.post('/api/scripts/shots/regenerate', { shotId, scriptText });
  }

  // === 配音生成 ===
  async generateVoice(voiceData) {
    return this.post('/api/voice/generate', voiceData);
  }

  async generateBatchVoice(voiceData) {
    return this.post('/api/voice/generate-batch', voiceData);
  }

  async getVoicePreview(voiceId) {
    return this.get(`/api/voice/preview/${voiceId}`);
  }

  async synthesizeText(textData) {
    return this.post('/api/voice/synthesize', textData);
  }

  // === 视频生成 ===
  async generateVideo(videoData) {
    return this.post('/api/video/generate', videoData);
  }

  async generateBatchVideo(videoData) {
    return this.post('/api/video/generate-batch', videoData);
  }

  async composeVideo(compositionData) {
    return this.post('/api/video/compose', compositionData);
  }

  async getVideoStatus(taskId) {
    return this.get(`/api/video/status/${taskId}`);
  }

  async exportVideo(exportData) {
    return this.post('/api/video/export', exportData);
  }

  // === 数字人/头像 ===
  async getAvatarList() {
    return this.get('/api/avatar/list');
  }

  async configureAvatar(avatarData) {
    return this.post('/api/avatar/configure', avatarData);
  }

  async generateAvatarVideo(avatarData) {
    return this.post('/api/avatar/generate', avatarData);
  }

  async getAvatarPreview(avatarId) {
    return this.get(`/api/avatar/preview/${avatarId}`);
  }

  async customizeAvatar(customData) {
    return this.post('/api/avatar/customize', customData);
  }

  // === 资源管理 ===
  async getAvatars() {
    return this.get('/api/resources/avatars');
  }

  async getVoices() {
    return this.get('/api/resources/voices');
  }

  // === 任务管理 ===
  async createTask(taskData) {
    return this.post('/api/tasks', taskData);
  }

  async getTask(taskId) {
    return this.get(`/api/tasks/${taskId}`);
  }

  async getProjectTasks(projectId) {
    return this.get(`/api/tasks/project/${projectId}`);
  }

  async cancelTask(taskId) {
    return this.delete(`/api/tasks/${taskId}`);
  }

  // === 健康检查 ===
  async healthCheck() {
    return this.get('/');
  }
}

// 创建单例实例
const apiService = new ApiService();

export default apiService;
