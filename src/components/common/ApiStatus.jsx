import { useEffect } from 'react';
import { Badge, Typography } from 'antd';
import { useApp } from '../../context/AppContext.jsx';
import { useApi } from '../../hooks/useApi.js';

const { Text } = Typography;

const ApiStatus = () => {
  const { state } = useApp();
  const { checkApiHealth } = useApi();

  useEffect(() => {
    // 初始检查
    checkApiHealth();

    // 定期检查API状态
    const interval = setInterval(() => {
      checkApiHealth();
    }, 30000); // 每30秒检查一次

    return () => clearInterval(interval);
  }, [checkApiHealth]);

  if (state.apiConnected) {
    return (
      <div className="flex items-center">
        <Badge status="success" />
        <Text type="success" className="text-sm ml-1">
          API已连接
        </Text>
      </div>
    );
  }

  return (
    <div className="flex items-center">
      <Badge status="error" />
      <Text type="danger" className="text-sm ml-1">
        API连接失败
      </Text>
    </div>
  );
};

export default ApiStatus;
