
import { Button as AntButton } from 'antd';

const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  onClick,
  className = '',
  icon,
  ...props
}) => {
  // 映射自定义变体到 Ant Design 的 type
  const getAntType = (variant) => {
    switch (variant) {
      case 'primary':
        return 'primary';
      case 'secondary':
        return 'default';
      case 'outline':
        return 'default';
      case 'danger':
        return 'primary';
      case 'ghost':
        return 'text';
      default:
        return 'primary';
    }
  };

  // 映射自定义尺寸到 Ant Design 的 size
  const getAntSize = (size) => {
    switch (size) {
      case 'sm':
        return 'small';
      case 'md':
        return 'middle';
      case 'lg':
        return 'large';
      default:
        return 'middle';
    }
  };

  // 处理危险按钮
  const isDanger = variant === 'danger';

  return (
    <AntButton
      type={getAntType(variant)}
      size={getAntSize(size)}
      disabled={disabled}
      onClick={onClick}
      className={className}
      danger={isDanger}
      icon={icon}
      {...props}
    >
      {children}
    </AntButton>
  );
};

export default Button;
