import { Alert } from 'antd';
import { CloseCircleOutlined } from '@ant-design/icons';
import { useApp } from '../../context/AppContext.jsx';

const ErrorDisplay = ({ className = "" }) => {
  const { state } = useApp();

  if (!state.error) return null;

  return (
    <div className={className}>
      <Alert
        message="操作失败"
        description={state.error}
        type="error"
        icon={<CloseCircleOutlined />}
        showIcon
        closable
        onClose={() => {
          // 可以在这里添加清除错误的逻辑
        }}
      />
    </div>
  );
};

export default ErrorDisplay;
