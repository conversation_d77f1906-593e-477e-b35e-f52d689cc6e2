import { useApp } from '../../context/AppContext.jsx';

const ErrorDisplay = ({ className = "" }) => {
  const { state } = useApp();

  if (!state.error) return null;

  return (
    <div className={`p-3 bg-red-50 border border-red-200 rounded-md ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <span className="text-red-400">❌</span>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">
            操作失败
          </h3>
          <div className="mt-1 text-sm text-red-700">
            {state.error}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ErrorDisplay;
