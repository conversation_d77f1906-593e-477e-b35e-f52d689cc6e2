import { Spin } from 'antd';

const LoadingSpinner = ({ size = "md", text = "加载中...", className = "" }) => {
  // 映射自定义尺寸到 Ant Design 的 size
  const getAntSize = (size) => {
    switch (size) {
      case 'sm':
        return 'small';
      case 'md':
        return 'default';
      case 'lg':
        return 'large';
      default:
        return 'default';
    }
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <Spin size={getAntSize(size)} tip={text} />
    </div>
  );
};

export default LoadingSpinner;
