import { Modal as AntModal } from 'antd';

const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  footer,
  size = 'md',
  className = '',
  ...props
}) => {
  // 映射自定义尺寸到 Ant Design 的 width
  const getWidth = (size) => {
    switch (size) {
      case 'sm':
        return 400;
      case 'md':
        return 520;
      case 'lg':
        return 800;
      case 'xl':
        return 1200;
      default:
        return 520;
    }
  };

  return (
    <AntModal
      title={title}
      open={isOpen}
      onCancel={onClose}
      footer={footer}
      width={getWidth(size)}
      className={className}
      destroyOnClose
      maskClosable
      keyboard
      {...props}
    >
      {children}
    </AntModal>
  );
};

export default Modal;
