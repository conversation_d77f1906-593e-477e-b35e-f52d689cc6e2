import { Steps, Progress, Typography } from 'antd';
import { useNavigation } from '../../hooks/useNavigation';

const { Text } = Typography;

const ProgressSteps = () => {
  const { tabs, getCompletedSteps, currentTab } = useNavigation();
  const completedSteps = getCompletedSteps();

  // 过滤掉API测试标签
  const workflowTabs = tabs.filter(tab => tab.id !== 'api-test');

  // 获取当前步骤索引
  const currentStepIndex = workflowTabs.findIndex(tab => tab.id === currentTab);

  // 转换为 Ant Design Steps 需要的格式
  const steps = workflowTabs.map((tab) => {
    const isCompleted = completedSteps.includes(tab.id);
    const isCurrent = tab.id === currentTab;

    let status = 'wait';
    if (isCompleted) {
      status = 'finish';
    } else if (isCurrent) {
      status = 'process';
    }

    return {
      title: tab.label,
      status: status,
    };
  });

  // 计算进度百分比
  const progressPercent = Math.round(((currentStepIndex + 1) / workflowTabs.length) * 100);

  // 获取当前步骤信息
  const currentStep = workflowTabs[currentStepIndex];

  return (
    <div className="w-full">
      {/* 移动端 - 进度条 + 当前步骤 */}
      <div className="block">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                {currentStepIndex + 1}
              </div>
              <Text strong className="text-sm">
                {currentStep?.label}
              </Text>
            </div>
            <Text type="secondary" className="text-xs">
              {progressPercent}%
            </Text>
          </div>

          <Progress
            percent={progressPercent}
            size="small"
            showInfo={false}
          />
        </div>
      </div>
    </div>
  );
};

export default ProgressSteps;
