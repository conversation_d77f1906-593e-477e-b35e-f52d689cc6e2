import { Steps } from 'antd';
import { useNavigation } from '../../hooks/useNavigation';

const ProgressSteps = () => {
  const { tabs, getCompletedSteps, currentTab } = useNavigation();
  const completedSteps = getCompletedSteps();

  // 过滤掉API测试标签
  const workflowTabs = tabs.filter(tab => tab.id !== 'api-test');

  // 获取当前步骤索引
  const currentStepIndex = workflowTabs.findIndex(tab => tab.id === currentTab);

  // 转换为 Ant Design Steps 需要的格式
  const steps = workflowTabs.map((tab) => {
    const isCompleted = completedSteps.includes(tab.id);
    const isCurrent = tab.id === currentTab;

    let status = 'wait';
    if (isCompleted) {
      status = 'finish';
    } else if (isCurrent) {
      status = 'process';
    }

    return {
      title: tab.label,
      status: status,
    };
  });

  return (
    <div className="w-full">
      <Steps
        current={currentStepIndex}
        items={steps}
        size="small"
        className="mb-4"
      />
    </div>
  );
};

export default ProgressSteps;
