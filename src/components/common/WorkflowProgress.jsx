import { useApp } from '../../context/AppContext.jsx';
import { useNavigation } from '../../hooks/useNavigation';

const WorkflowProgress = () => {
  const { state } = useApp();
  const { tabs, currentTab } = useNavigation();

  // 定义工作流程步骤的完成状态
  const getStepStatus = (tabId) => {
    switch (tabId) {
      case 'upload':
        return state.projectName && (state.textMaterial || state.uploadedFiles.length > 0) ? 'completed' : 'current';
      case 'script':
        return state.scripts.length > 0 ? 'completed' : 'pending';
      case 'storyboard':
        return state.shots.length > 0 ? 'completed' : 'pending';
      case 'voice':
        return state.voiceGenerated ? 'completed' : 'pending';
      case 'video':
        return state.videoGenerated ? 'completed' : 'pending';
      case 'avatar':
        return state.avatarConfigured ? 'completed' : 'pending';
      case 'compose':
        return state.videoComposed ? 'completed' : 'pending';
      case 'export':
        return 'pending';
      default:
        return 'pending';
    }
  };

  const getCurrentStepIndex = () => {
    return tabs.findIndex(tab => tab.id === currentTab);
  };

  const workflowTabs = tabs.filter(tab => tab.id !== 'api-test');

  return (
    <div className="bg-white border-b border-gray-200 px-8 py-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-900">工作流程进度</h3>
        <div className="flex items-center space-x-4">
          {workflowTabs.map((tab, index) => {
            const status = getStepStatus(tab.id);
            const isCurrent = tab.id === currentTab;
            const currentIndex = getCurrentStepIndex();
            
            return (
              <div key={tab.id} className="flex items-center">
                <div className="flex items-center">
                  <div className={`
                    flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium
                    ${status === 'completed' 
                      ? 'bg-green-500 text-white' 
                      : isCurrent 
                        ? 'bg-blue-500 text-white'
                        : index <= currentIndex
                          ? 'bg-gray-300 text-gray-700'
                          : 'bg-gray-100 text-gray-400'
                    }
                  `}>
                    {status === 'completed' ? '✓' : index + 1}
                  </div>
                  <span className={`
                    ml-2 text-xs font-medium
                    ${isCurrent ? 'text-blue-600' : 'text-gray-500'}
                  `}>
                    {tab.label}
                  </span>
                </div>
                {index < workflowTabs.length - 1 && (
                  <div className={`
                    w-8 h-0.5 mx-2
                    ${index < currentIndex ? 'bg-green-500' : 'bg-gray-200'}
                  `} />
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default WorkflowProgress;
