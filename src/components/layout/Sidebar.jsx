import { useState } from 'react';
import { Drawer } from 'antd';
import { MenuOutlined } from '@ant-design/icons';
import { useNavigation } from '../../hooks/useNavigation';

const Sidebar = () => {
  const { tabs, currentTab, setCurrentTab, canNavigateToTab } = useNavigation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleTabClick = (tabId) => {
    if (canNavigateToTab(tabId)) {
      setCurrentTab(tabId);
      // 在移动端点击后关闭菜单
      setMobileMenuOpen(false);
    }
  };

  // 导航内容组件
  const NavigationContent = ({ isCollapsed = false }) => (
    <div className="flex flex-col h-full">
      {/* Logo */}
      <div className={`p-5 mb-8 flex items-center ${isCollapsed ? 'justify-center' : 'gap-3'}`}>
        <img
          src="https://images.unsplash.com/photo-1501854140801-50d01698950b?w=150&h=100&fit=crop&q=80"
          alt="AI视频生成平台"
          className="w-8 h-8 rounded flex-shrink-0"
        />
        {!isCollapsed && (
          <h1 className="text-lg font-semibold whitespace-nowrap">AI视频生成平台</h1>
        )}
      </div>

      {/* Navigation Links */}
      <div className="flex flex-col gap-1 flex-1">
        {tabs.map((tab) => {
          const isActive = currentTab === tab.id;
          const canNavigate = canNavigateToTab(tab.id);

          return (
            <button
              key={tab.id}
              onClick={() => handleTabClick(tab.id)}
              disabled={!canNavigate}
              className={`
                flex items-center gap-3 px-5 py-3 text-left transition-all duration-300
                ${isActive
                  ? 'bg-gray-800 text-white border-l-3 border-blue-500'
                  : canNavigate
                    ? 'text-gray-300 hover:bg-gray-800 hover:text-white'
                    : 'text-gray-500 cursor-not-allowed'
                }
                ${isCollapsed ? 'justify-center px-3' : ''}
              `}
              title={isCollapsed ? tab.label : ''}
            >
              <span className="material-icons text-xl flex-shrink-0">{tab.icon}</span>
              {!isCollapsed && (
                <span className="font-medium whitespace-nowrap">{tab.label}</span>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );

  return (
    <>
      {/* 移动端汉堡菜单按钮 */}
      <button
        className="lg:hidden fixed top-4 left-4 z-50 p-2 bg-gray-900 text-white rounded-md shadow-lg"
        onClick={() => setMobileMenuOpen(true)}
      >
        <MenuOutlined className="text-xl" />
      </button>

      {/* 桌面端侧边栏 - 固定宽度 */}
      <nav className="hidden lg:flex w-60 bg-gray-900 text-white flex-col">
        <NavigationContent />
      </nav>

      {/* 平板端侧边栏 - 收缩模式 */}
      <nav className="hidden md:flex lg:hidden w-16 bg-gray-900 text-white flex-col">
        <NavigationContent isCollapsed={true} />
      </nav>

      {/* 移动端抽屉菜单 */}
      <Drawer
        title={
          <div className="flex items-center gap-3">
            <img
              src="https://images.unsplash.com/photo-1501854140801-50d01698950b?w=150&h=100&fit=crop&q=80"
              alt="AI视频生成平台"
              className="w-8 h-8 rounded"
            />
            <span className="text-lg font-semibold">AI视频生成平台</span>
          </div>
        }
        placement="left"
        onClose={() => setMobileMenuOpen(false)}
        open={mobileMenuOpen}
        className="md:hidden"
        width={280}
        styles={{
          body: { padding: 0, backgroundColor: '#111827' },
          header: { backgroundColor: '#111827', borderBottom: '1px solid #374151' }
        }}
      >
        <div className="bg-gray-900 text-white h-full">
          <NavigationContent />
        </div>
      </Drawer>

      {/* 移动端底部导航栏 */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-gray-900 text-white border-t border-gray-700 z-40">
        <div className="flex justify-around items-center py-2">
          {tabs.slice(0, 5).map((tab) => {
            const isActive = currentTab === tab.id;
            const canNavigate = canNavigateToTab(tab.id);

            return (
              <button
                key={tab.id}
                onClick={() => handleTabClick(tab.id)}
                disabled={!canNavigate}
                className={`
                  flex flex-col items-center gap-1 px-2 py-2 transition-all duration-300
                  ${isActive
                    ? 'text-blue-400'
                    : canNavigate
                      ? 'text-gray-300 hover:text-white'
                      : 'text-gray-500 cursor-not-allowed'
                  }
                `}
              >
                <span className="material-icons text-lg">{tab.icon}</span>
                <span className="text-xs font-medium">{tab.label}</span>
              </button>
            );
          })}
          {tabs.length > 5 && (
            <button
              onClick={() => setMobileMenuOpen(true)}
              className="flex flex-col items-center gap-1 px-2 py-2 text-gray-300 hover:text-white transition-all duration-300"
            >
              <MenuOutlined className="text-lg" />
              <span className="text-xs font-medium">更多</span>
            </button>
          )}
        </div>
      </div>
    </>
  );
};

export default Sidebar;
