import { useApp } from '../../context/AppContext.jsx';
import { useNavigate } from 'react-router-dom';
import ProgressSteps from '../common/ProgressSteps';
import Button from '../common/Button';
import ApiStatus from '../common/ApiStatus';

const StatusBar = () => {
  const { state } = useApp();
  const navigate = useNavigate();

  const handleSaveProject = () => {
    console.log('Saving project...');
    // Implement save logic here
  };

  return (
    <div className="bg-white border-b border-gray-200 px-8 py-4 flex justify-between items-center">
      {/* Progress Steps */}
      <ProgressSteps />
      
      {/* User Info */}
      <div className="flex items-center gap-4">
        <ApiStatus />
        <span className="text-sm text-gray-500">
          {state.projectName}
        </span>
        <Button
          variant="secondary"
          size="sm"
          onClick={handleSaveProject}
        >
          保存
        </Button>
        <Button
          variant="secondary"
          size="sm"
          onClick={() => {
            navigate('/');
          }}
        >
          退出
        </Button>
      </div>
    </div>
  );
};

export default StatusBar;
