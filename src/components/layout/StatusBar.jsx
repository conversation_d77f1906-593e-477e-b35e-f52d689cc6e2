import { useApp } from '../../context/AppContext.jsx';
import { useNavigate } from 'react-router-dom';
import ProgressSteps from '../common/ProgressSteps';
import Button from '../common/Button';
import ApiStatus from '../common/ApiStatus';

const StatusBar = () => {
  const { state } = useApp();
  const navigate = useNavigate();

  const handleSaveProject = () => {
    console.log('Saving project...');
    // Implement save logic here
  };

  return (
    <div className="bg-white border-b border-gray-200 px-4 md:px-6 lg:px-8 py-3 md:py-4">
      {/* 桌面端布局 */}
      <div className="hidden md:flex justify-between items-center">
        {/* Progress Steps */}
        <div className="flex-1 mr-4">
          <ProgressSteps />
        </div>

        {/* User Info */}
        <div className="flex items-center gap-2 lg:gap-4 flex-shrink-0">
          <ApiStatus />
          <span className="text-sm text-gray-500 hidden lg:inline max-w-32 truncate">
            {state.projectName}
          </span>
          <Button
            variant="secondary"
            size="sm"
            onClick={handleSaveProject}
          >
            保存
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => {
              navigate('/');
            }}
          >
            退出
          </Button>
        </div>
      </div>

      {/* 移动端布局 */}
      <div className="md:hidden">
        {/* 顶部行：项目名称和操作按钮 */}
        <div className="flex justify-between items-center mb-3">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <ApiStatus />
            <span className="text-sm text-gray-500 truncate">
              {state.projectName || '未命名项目'}
            </span>
          </div>

          <div className="flex items-center gap-2 flex-shrink-0">
            <Button
              variant="secondary"
              size="sm"
              onClick={handleSaveProject}
            >
              保存
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => {
                navigate('/');
              }}
            >
              退出
            </Button>
          </div>
        </div>

        {/* 底部行：进度步骤 */}
        <ProgressSteps />
      </div>
    </div>
  );
};

export default StatusBar;
