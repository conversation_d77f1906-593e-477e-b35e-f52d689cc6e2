import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useApp } from '../../context/AppContext.jsx';
import { useApi } from '../../hooks/useApi.js';
import { useToast } from '../../hooks/useToast.js';
import Button from '../common/Button';
import LoadingSpinner from '../common/LoadingSpinner';
import ErrorDisplay from '../common/ErrorDisplay';
import MaterialSourceSelector from '../upload/MaterialSourceSelector';
import FileUpload from '../upload/FileUpload';

const NewProject = () => {
  const navigate = useNavigate();
  const { state, dispatch, ActionTypes } = useApp();
  const { createProject, generateScripts } = useApi();
  const { showSuccess, showError } = useToast();
  const [isCreating, setIsCreating] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleInputChange = (field, value) => {
    dispatch({
      type: ActionTypes.UPDATE_PROJECT_INFO,
      payload: { [field]: value }
    });
  };

  const handleTextMaterialChange = (value) => {
    dispatch({ type: ActionTypes.SET_TEXT_MATERIAL, payload: value });
  };

  const validateForm = () => {
    if (!state.projectName?.trim()) {
      showError('请输入项目名称');
      return false;
    }

    if (state.materialSource === 'text' && !state.textMaterial?.trim()) {
      showError('请输入文本素材');
      return false;
    }

    if (state.materialSource === 'upload' && state.uploadedFiles.length === 0) {
      showError('请上传素材文件');
      return false;
    }

    return true;
  };

  const handleSaveProject = async () => {
    if (!validateForm()) return;

    setIsCreating(true);
    try {
      const projectData = {
        name: state.projectName,
        theme: state.projectTheme,
        requirements: state.projectRequirements
      };

      const result = await createProject(projectData);
      showSuccess('项目保存成功');
      
      // 重定向到项目页面
      navigate(`/project/${result.id}`);
    } catch (error) {
      console.error('保存项目失败:', error);
      showError('保存项目失败，请重试');
    } finally {
      setIsCreating(false);
    }
  };

  const handleSaveDraft = async () => {
    setIsCreating(true);
    try {
      const projectData = {
        name: state.projectName || '未命名项目',
        theme: state.projectTheme,
        requirements: state.projectRequirements,
        isDraft: true
      };

      const result = await createProject(projectData);
      showSuccess('草稿保存成功');
      
      // 重定向到项目页面
      navigate(`/project/${result.id}`);
    } catch (error) {
      console.error('保存草稿失败:', error);
      showError('保存草稿失败，请重试');
    } finally {
      setIsCreating(false);
    }
  };

  const handleStartGeneration = async () => {
    if (!validateForm()) return;

    setIsGenerating(true);
    try {
      // 先创建项目
      const projectData = {
        name: state.projectName,
        theme: state.projectTheme,
        requirements: state.projectRequirements
      };

      const projectResult = await createProject(projectData);
      
      // 生成脚本
      const scriptData = {
        projectName: state.projectName,
        projectTheme: state.projectTheme,
        projectRequirements: state.projectRequirements,
        materialSource: state.materialSource,
        uploadedFiles: state.uploadedFiles,
        textMaterial: state.textMaterial
      };

      await generateScripts(scriptData);
      showSuccess('项目创建成功，脚本生成完成');
      
      // 重定向到项目页面的脚本标签
      navigate(`/project/${projectResult.id}?tab=script`);
    } catch (error) {
      console.error('生成脚本失败:', error);
      showError('生成脚本失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleBack = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <Button
              variant="secondary"
              onClick={handleBack}
              className="mr-4"
            >
              <span className="material-icons mr-2">arrow_back</span>
              返回
            </Button>
            <h1 className="text-3xl font-bold text-gray-900">创建新项目</h1>
          </div>
          <p className="text-gray-600">
            填写项目信息并上传相关素材，AI将为您生成精彩视频
          </p>
        </div>

        {/* 错误显示 */}
        <ErrorDisplay className="mb-6" />

        {/* 表单容器 */}
        <div className="bg-white rounded-lg shadow-sm p-8">
          {/* 项目名称 */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              项目名称 *
            </label>
            <input
              type="text"
              value={state.projectName}
              onChange={(e) => handleInputChange('projectName', e.target.value)}
              placeholder="给项目起个名字"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* 项目主题 */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              项目主题
            </label>
            <input
              type="text"
              value={state.projectTheme}
              onChange={(e) => handleInputChange('projectTheme', e.target.value)}
              placeholder="例如：科技、教育、商务等"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* 项目需求 */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              项目需求
            </label>
            <textarea
              value={state.projectRequirements}
              onChange={(e) => handleInputChange('projectRequirements', e.target.value)}
              placeholder="描述您的具体需求和期望效果..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
            />
          </div>

          {/* 素材来源选择 */}
          <MaterialSourceSelector />

          {/* 文件上传或文本输入 */}
          {state.materialSource === 'upload' ? (
            <FileUpload />
          ) : (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                文本素材 *
              </label>
              <textarea
                value={state.textMaterial}
                onChange={(e) => handleTextMaterialChange(e.target.value)}
                placeholder="请在此处粘贴或输入您的文本素材，例如文章、大纲、核心要点等..."
                rows={8}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
              />
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end gap-4 pt-6 border-t border-gray-200">
            <Button
              variant="secondary"
              onClick={handleSaveDraft}
              disabled={isCreating || isGenerating || state.isLoading}
            >
              {isCreating ? (
                <div className="flex items-center">
                  <LoadingSpinner size="sm" text="" className="mr-2" />
                  保存中...
                </div>
              ) : (
                '保存草稿'
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={handleSaveProject}
              disabled={isCreating || isGenerating || state.isLoading}
            >
              保存项目
            </Button>
            
            <Button
              variant="primary"
              onClick={handleStartGeneration}
              disabled={isCreating || isGenerating || state.isLoading}
            >
              {isGenerating ? (
                <div className="flex items-center">
                  <LoadingSpinner size="sm" text="" className="mr-2" />
                  生成中...
                </div>
              ) : (
                '开始生成脚本'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewProject;
