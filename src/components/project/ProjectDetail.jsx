import { useEffect, useState } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { useApp } from '../../context/AppContext.jsx';
import { useApi } from '../../hooks/useApi.js';
import { useToast } from '../../hooks/useToast.js';
import MainLayout from '../layout/MainLayout';
import LoadingSpinner from '../common/LoadingSpinner';
import ErrorDisplay from '../common/ErrorDisplay';

const ProjectDetail = () => {
  const { projectId } = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { state, dispatch, ActionTypes } = useApp();
  const { getProject, checkApiHealth } = useApi();
  const { showError } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [projectNotFound, setProjectNotFound] = useState(false);

  useEffect(() => {
    const loadProject = async () => {
      if (!projectId) {
        navigate('/');
        return;
      }

      setIsLoading(true);
      try {
        // 检查API连接
        await checkApiHealth();

        // 加载项目信息
        const project = await getProject(projectId);
        
        if (project) {
          // 更新AppContext中的项目信息
          dispatch({ type: ActionTypes.SET_PROJECT_ID, payload: project.id });
          dispatch({ 
            type: ActionTypes.UPDATE_PROJECT_INFO, 
            payload: {
              projectName: project.name,
              projectTheme: project.theme || '',
              projectRequirements: project.requirements || ''
            }
          });

          // 检查URL参数中的tab，设置当前标签
          const tab = searchParams.get('tab');
          const targetTab = tab || 'upload'; // 默认为upload

          // 只有当目标tab与当前状态不同时才更新
          if (targetTab !== state.currentTab) {
            dispatch({ type: ActionTypes.SET_CURRENT_TAB, payload: targetTab });
          }

          // 如果URL中没有tab参数，添加默认的tab参数
          if (!tab) {
            const newSearchParams = new URLSearchParams(searchParams);
            newSearchParams.set('tab', 'upload');
            navigate(`/project/${projectId}?${newSearchParams.toString()}`, { replace: true });
          }
        } else {
          setProjectNotFound(true);
        }
      } catch (error) {
        console.error('加载项目失败:', error);
        if (error.message.includes('404') || error.message.includes('not found')) {
          setProjectNotFound(true);
        } else {
          showError('加载项目失败，请重试');
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadProject();
  }, [projectId, searchParams, dispatch, getProject, checkApiHealth, navigate, showError, ActionTypes, state.currentTab]);

  // 处理应用内tab切换时的URL更新
  useEffect(() => {
    const currentUrlTab = searchParams.get('tab');

    // 只有当状态中的tab与URL中的tab不同，且不是初始加载时才更新URL
    if (state.currentTab && currentUrlTab && currentUrlTab !== state.currentTab) {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set('tab', state.currentTab);
      navigate(`/project/${projectId}?${newSearchParams.toString()}`, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.currentTab, projectId, navigate]); // 故意不包含searchParams以避免循环

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" text="正在加载项目..." />
      </div>
    );
  }

  if (projectNotFound) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="material-icons text-2xl text-red-600">error</span>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">项目不存在</h2>
          <p className="text-gray-600 mb-6">
            您访问的项目不存在或已被删除
          </p>
          <button
            onClick={() => navigate('/')}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            返回首页
          </button>
        </div>
      </div>
    );
  }

  return <MainLayout />;
};

export default ProjectDetail;
