import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useApp } from '../../context/AppContext.jsx';
import { useApi } from '../../hooks/useApi.js';
import { useToast } from '../../hooks/useToast.js';
import Button from '../common/Button';
import LoadingSpinner from '../common/LoadingSpinner';
import ErrorDisplay from '../common/ErrorDisplay';

const ProjectSelector = () => {
  const navigate = useNavigate();
  const { state } = useApp();
  const { checkApiHealth, getAllProjects, createProject, deleteProject } = useApi();
  const { showSuccess, showError } = useToast();
  const [projects, setProjects] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [deletingProjectId, setDeletingProjectId] = useState(null);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // 检查API连接
        await checkApiHealth();

        // 加载已有项目列表
        const projectList = await getAllProjects();
        setProjects(projectList || []);
      } catch (error) {
        console.error('初始化应用失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeApp();
  }, [checkApiHealth, getAllProjects, createProject]);

  const handleCreateNewProject = async () => {
    setIsCreating(true);
    try {
      // 直接创建一个新项目
      const projectData = {
        name: `新项目 ${new Date().toLocaleString()}`,
        theme: '',
        requirements: '',
        isDraft: true
      };

      const result = await createProject(projectData);
      showSuccess('项目创建成功');

      // 直接跳转到项目的上传内容页面
      navigate(`/project/${result.id}?tab=upload`);
    } catch (error) {
      console.error('创建项目失败:', error);
      showError('创建项目失败，请重试');
    } finally {
      setIsCreating(false);
    }
  };

  const handleSelectProject = (projectId) => {
    navigate(`/project/${projectId}?tab=upload`);
  };

  const handleDeleteProject = async (projectId, projectName) => {
    if (!confirm(`确定要删除项目"${projectName}"吗？此操作不可撤销。`)) {
      return;
    }

    setDeletingProjectId(projectId);
    try {
      await deleteProject(projectId);
      showSuccess('项目删除成功');

      // 重新加载项目列表
      const updatedProjects = await getAllProjects();
      setProjects(updatedProjects || []);
    } catch (error) {
      console.error('删除项目失败:', error);
      showError('删除项目失败，请重试');
    } finally {
      setDeletingProjectId(null);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" text="正在初始化应用..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            AI视频生成平台
          </h1>
          <p className="text-xl text-gray-600">
            选择一个项目开始创作，或创建新的视频项目
          </p>
        </div>

        {/* API连接状态 */}
        {!state.apiConnected && (
          <div className="mb-8">
            <ErrorDisplay className="max-w-2xl mx-auto" />
          </div>
        )}

        {/* 创建新项目区域 */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="bg-white rounded-lg shadow-sm border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors">
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="material-icons text-2xl text-blue-600">add</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                创建新项目
              </h3>
              <p className="text-gray-600 mb-6">
                开始一个全新的AI视频生成项目
              </p>
              <Button
                variant="primary"
                onClick={handleCreateNewProject}
                disabled={!state.apiConnected || isCreating}
                className="w-full max-w-xs"
              >
                {isCreating ? (
                  <div className="flex items-center">
                    <LoadingSpinner size="sm" text="" className="mr-2" />
                    创建中...
                  </div>
                ) : (
                  '开始创建'
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* 最近项目区域 */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center">
                <span className="material-icons text-2xl text-gray-600 mr-2">folder</span>
                <h3 className="text-xl font-semibold text-gray-900">
                  最近项目
                </h3>
                {projects.length > 0 && (
                  <span className="ml-2 text-sm text-gray-500">
                    ({projects.length} 个项目)
                  </span>
                )}
              </div>
            </div>

            <div className="p-6">
              {projects.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="material-icons text-2xl text-gray-400">folder_open</span>
                  </div>
                  <p className="text-gray-500 mb-2">暂无已有项目</p>
                  <p className="text-sm text-gray-400">
                    创建您的第一个项目开始使用
                  </p>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
                  {projects.map((project) => (
                    <div
                      key={project.id}
                      className="group relative p-4 border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200"
                    >
                      <div
                        className="cursor-pointer"
                        onClick={() => handleSelectProject(project.id)}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-1">
                            {project.name}
                          </h4>
                        </div>
                        <p className="text-sm text-gray-500 mb-3">
                          创建于 {new Date(project.createdAt).toLocaleDateString()}
                        </p>
                        {project.theme && (
                          <p className="text-xs text-gray-400 mb-2">
                            主题: {project.theme}
                          </p>
                        )}
                        {project.isDraft && (
                          <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                            草稿
                          </span>
                        )}
                      </div>

                      {/* 删除按钮 */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteProject(project.id, project.name);
                        }}
                        disabled={deletingProjectId === project.id}
                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded"
                        title="删除项目"
                      >
                        {deletingProjectId === project.id ? (
                          <LoadingSpinner size="sm" text="" />
                        ) : (
                          <span className="material-icons text-sm">delete</span>
                        )}
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 底部信息 */}
        <div className="text-center mt-12 text-gray-500">
          <p className="text-sm">
            AI视频生成平台 v1.0.0 | 
            <span className={`ml-2 ${state.apiConnected ? 'text-green-600' : 'text-red-600'}`}>
              {state.apiConnected ? '服务正常' : '服务异常'}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProjectSelector;
