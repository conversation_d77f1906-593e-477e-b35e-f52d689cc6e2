import { useState } from 'react';
import { useApi } from '../../hooks/useApi.js';
import { useApp } from '../../context/AppContext.jsx';
import Button from '../common/Button';

const ApiTest = () => {
  const { state } = useApp();
  const {
    checkApiHealth,
    loadVoices,
    loadAvatars,
    generateScripts,
    generateShots,
    createProject
  } = useApi();
  
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (test, result, success = true) => {
    setTestResults(prev => ({
      ...prev,
      [test]: { result, success, timestamp: new Date().toLocaleTimeString() }
    }));
  };

  const runTest = async (testName, testFunction) => {
    try {
      addResult(testName, '测试中...', true);
      const result = await testFunction();
      addResult(testName, JSON.stringify(result, null, 2), true);
    } catch (error) {
      addResult(testName, `错误: ${error.message}`, false);
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults({});

    try {
      // 1. 健康检查
      await runTest('健康检查', checkApiHealth);

      // 2. 加载声音列表
      await runTest('加载声音列表', loadVoices);

      // 3. 加载头像列表
      await runTest('加载头像列表', loadAvatars);

      // 4. 创建测试项目
      await runTest('创建项目', () =>
        createProject({
          name: '测试项目 ' + Date.now(),
          theme: '科技',
          requirements: '测试项目需求'
        })
      );

      // 5. 生成脚本
      let generatedScripts = [];
      await runTest('生成脚本', async () => {
        const result = await generateScripts({
          projectName: '测试项目',
          materialSource: 'text',
          textMaterial: '这是一个测试文本，用于验证脚本生成功能。'
        });
        generatedScripts = result.scripts || [];
        return result;
      });

      // 6. 生成镜头（如果脚本生成成功）
      if (generatedScripts.length > 0) {
        await runTest('生成镜头', () => generateShots(generatedScripts));
      }

    } catch (error) {
      console.error('测试过程中出现错误:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const TestResult = ({ name, result }) => {
    if (!result) return null;

    return (
      <div className="mb-4 p-4 border rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-medium">{name}</h4>
          <div className="flex items-center gap-2">
            <span className={`w-2 h-2 rounded-full ${result.success ? 'bg-green-500' : 'bg-red-500'}`}></span>
            <span className="text-sm text-gray-500">{result.timestamp}</span>
          </div>
        </div>
        <pre className={`text-xs p-2 rounded ${result.success ? 'bg-green-50' : 'bg-red-50'} overflow-auto max-h-40`}>
          {result.result}
        </pre>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">API集成测试</h2>
        <p className="text-gray-600">测试前端与后端API的连接和功能</p>
      </div>

      {/* API状态 */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium mb-2">API状态</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">连接状态: </span>
            <span className={state.apiConnected ? 'text-green-600' : 'text-red-600'}>
              {state.apiConnected ? '已连接' : '未连接'}
            </span>
          </div>
          <div>
            <span className="text-gray-600">加载状态: </span>
            <span className={state.isLoading ? 'text-blue-600' : 'text-gray-600'}>
              {state.isLoading ? '加载中' : '空闲'}
            </span>
          </div>
          <div>
            <span className="text-gray-600">项目ID: </span>
            <span>{state.projectId || '未创建'}</span>
          </div>
          <div>
            <span className="text-gray-600">错误信息: </span>
            <span className="text-red-600">{state.error || '无'}</span>
          </div>
        </div>
      </div>

      {/* 测试控制 */}
      <div className="mb-6">
        <Button 
          onClick={runAllTests} 
          disabled={isRunning}
          variant="primary"
        >
          {isRunning ? '测试进行中...' : '运行所有测试'}
        </Button>
      </div>

      {/* 测试结果 */}
      <div>
        <h3 className="font-medium mb-4">测试结果</h3>
        {Object.entries(testResults).map(([name, result]) => (
          <TestResult key={name} name={name} result={result} />
        ))}
        
        {Object.keys(testResults).length === 0 && !isRunning && (
          <p className="text-gray-500 text-center py-8">
            点击"运行所有测试"开始测试API功能
          </p>
        )}
      </div>
    </div>
  );
};

export default ApiTest;
