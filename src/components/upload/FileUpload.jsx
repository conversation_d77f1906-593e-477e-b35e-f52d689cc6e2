import { Upload, But<PERSON> as Ant<PERSON><PERSON><PERSON>, Typography } from 'antd';
import { InboxOutlined, DeleteOutlined } from '@ant-design/icons';
import { useFileUpload } from '../../hooks/useFileUpload';

const { Dragger } = Upload;
const { Text } = Typography;

const FileUpload = () => {
  const {
    uploadedFiles,
    handleFileSelect,
    removeFile
  } = useFileUpload();

  const uploadProps = {
    name: 'file',
    multiple: true,
    showUploadList: false,
    beforeUpload: (file) => {
      handleFileSelect([file]);
      return false; // 阻止自动上传
    },
    onDrop: (e) => {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        上传素材
      </label>

      {/* Upload Area */}
      <Dragger {...uploadProps} className="mb-4">
        <p className="ant-upload-drag-icon">
          <InboxOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
        </p>
        <p className="ant-upload-text">点击或拖拽文件到此处上传</p>
        <p className="ant-upload-hint">
          支持单个或批量上传。支持文档格式
        </p>
      </Dragger>

        {/* Uploaded Files List */}
        {uploadedFiles.length > 0 && (
          <div className="space-y-2">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center justify-between bg-gray-50 p-3 rounded-md border border-gray-200"
              >
                <div className="flex-1">
                  <Text strong className="block" title={file.name}>
                    {file.name}
                  </Text>
                  <Text type="secondary" className="text-xs">
                    {file.formattedSize}
                  </Text>
                </div>
                <AntButton
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => removeFile(file.id)}
                  title="删除文件"
                  size="small"
                />
              </div>
            ))}
          </div>
        )}
    </div>
  );
};

export default FileUpload;
