import { Radio } from 'antd';
import { useApp } from '../../context/AppContext.jsx';

const MaterialSourceSelector = () => {
  const { state, dispatch, ActionTypes } = useApp();

  const handleSourceChange = (e) => {
    dispatch({ type: ActionTypes.SET_MATERIAL_SOURCE, payload: e.target.value });
  };

  const options = [
    { label: '上传文件', value: 'upload' },
    { label: '输入文本', value: 'text' },
  ];

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        素材来源
      </label>
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 w-fit">
        <Radio.Group
          options={options}
          onChange={handleSourceChange}
          value={state.materialSource}
          optionType="button"
          buttonStyle="solid"
          size="large"
        />
      </div>
    </div>
  );
};

export default MaterialSourceSelector;
