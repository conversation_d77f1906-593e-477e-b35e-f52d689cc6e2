import { useState, useEffect } from 'react';
import { Input, message, Typography } from 'antd';
import { useApp } from '../../context/AppContext.jsx';
import { useNavigation } from '../../hooks/useNavigation';
import { useApi } from '../../hooks/useApi.js';
import Button from '../common/Button';
import ErrorDisplay from '../common/ErrorDisplay';
import LoadingSpinner from '../common/LoadingSpinner';
import MaterialSourceSelector from './MaterialSourceSelector';
import FileUpload from './FileUpload';

const { TextArea } = Input;
const { Title, Paragraph } = Typography;

const UploadContent = () => {
  const { state, dispatch, ActionTypes } = useApp();
  const { setCurrentTab } = useNavigation();
  const { createProject, updateProject, generateScripts, checkApiHealth } = useApi();
  const [isGenerating, setIsGenerating] = useState(false);

  // 检查API连接状态
  useEffect(() => {
    checkApiHealth();
  }, [checkApiHealth]);

  const handleInputChange = (field, value) => {
    dispatch({
      type: ActionTypes.UPDATE_PROJECT_INFO,
      payload: { [field]: value }
    });
  };

  const handleTextMaterialChange = (value) => {
    dispatch({ type: ActionTypes.SET_TEXT_MATERIAL, payload: value });
  };

  const handleSaveDraft = async () => {
    try {
      const projectData = {
        name: state.projectName || '未命名项目',
        theme: state.projectTheme,
        requirements: state.projectRequirements,
        materialSource: state.materialSource,
        textMaterial: state.textMaterial,
        uploadedFiles: state.uploadedFiles
      };

      if (state.projectId) {
        // 如果已有项目ID，更新现有项目
        await updateProject(state.projectId, projectData);
        message.success('项目草稿已保存');
      } else {
        // 如果没有项目ID，创建新项目
        const result = await createProject({ ...projectData, isDraft: true });
        dispatch({ type: ActionTypes.SET_PROJECT_ID, payload: result.id });
        message.success('项目草稿已保存');
      }
    } catch (error) {
      console.error('保存草稿失败:', error);
      message.error('保存草稿失败，请重试');
    }
  };

  const handleStartGeneration = async () => {
    // 验证输入
    if (state.materialSource === 'text' && !state.textMaterial.trim()) {
      message.warning('请输入文本素材');
      return;
    }

    if (state.materialSource === 'upload' && state.uploadedFiles.length === 0) {
      message.warning('请上传素材文件');
      return;
    }

    setIsGenerating(true);
    try {
      let currentProjectId = state.projectId;

      // 创建或更新项目
      const projectData = {
        name: state.projectName || '未命名项目',
        theme: state.projectTheme,
        requirements: state.projectRequirements,
        materialSource: state.materialSource,
        textMaterial: state.textMaterial,
        uploadedFiles: state.uploadedFiles
      };

      if (!currentProjectId) {
        // 如果没有项目ID，创建新项目
        const result = await createProject(projectData);
        currentProjectId = result.id;
        dispatch({ type: ActionTypes.SET_PROJECT_ID, payload: currentProjectId });
      } else {
        // 如果已有项目ID，更新现有项目
        await updateProject(currentProjectId, projectData);
      }

      // 生成脚本
      const scriptData = {
        projectId: currentProjectId,
        projectName: state.projectName,
        projectTheme: state.projectTheme,
        projectRequirements: state.projectRequirements,
        materialSource: state.materialSource,
        uploadedFiles: state.uploadedFiles,
        textMaterial: state.textMaterial
      };

      await generateScripts(scriptData);

      // 导航到脚本生成页面
      setCurrentTab('script');
    } catch (error) {
      console.error('生成脚本失败:', error);
      message.error('生成脚本失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          创建新项目
        </h2>
        <p className="text-gray-600">
          填写项目信息并上传相关素材，AI将为您生成精彩视频
        </p>
      </div>

      {/* Form Container */}
      <div className="max-w-4xl bg-white rounded-lg p-8 shadow-sm">
        {/* Project Name */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            项目名称
          </label>
          <Input
            value={state.projectName}
            onChange={(e) => handleInputChange('projectName', e.target.value)}
            placeholder="给项目起个名字"
            size="large"
          />
        </div>

        {/* Project Theme */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            视频主题
          </label>
          <Input
            value={state.projectTheme}
            onChange={(e) => handleInputChange('projectTheme', e.target.value)}
            placeholder="例如：产品介绍、企业宣传、教育培训等"
            size="large"
          />
        </div>

        {/* Project Requirements */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            详细需求
          </label>
          <TextArea
            value={state.projectRequirements}
            onChange={(e) => handleInputChange('projectRequirements', e.target.value)}
            placeholder="描述您对视频的具体要求，包括目标受众、核心信息、时长等"
            rows={4}
            size="large"
          />
        </div>

        {/* Material Source Selector */}
        <MaterialSourceSelector />

        {/* File Upload or Text Input */}
        {state.materialSource === 'upload' ? (
          <FileUpload />
        ) : (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              文本素材
            </label>
            <TextArea
              value={state.textMaterial}
              onChange={(e) => handleTextMaterialChange(e.target.value)}
              placeholder="请在此处粘贴或输入您的文本素材，例如文章、大纲、核心要点等..."
              rows={8}
              size="large"
            />
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-4">
          <Button
            variant="secondary"
            onClick={handleSaveDraft}
            disabled={state.isLoading}
          >
            保存草稿
          </Button>
          <Button
            variant="primary"
            onClick={handleStartGeneration}
            disabled={isGenerating || state.isLoading}
          >
            {(isGenerating || state.isLoading) ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" text="" className="mr-2" />
                正在生成脚本...
              </div>
            ) : (
              '开始生成脚本'
            )}
          </Button>
        </div>

        {/* 错误信息显示 */}
        <ErrorDisplay className="mt-4" />

        {/* API连接状态指示器 */}
        {!state.apiConnected && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">
              ⚠️ 后端API连接异常，请确保后端服务正在运行 (http://localhost:3001)
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default UploadContent;
