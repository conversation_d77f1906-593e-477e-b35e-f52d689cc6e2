const { nanoid } = require('nanoid');

// 可用的数字人角色
const availableAvatars = [
  {
    id: 'emma',
    name: '艾玛',
    gender: 'female',
    age: 'young-adult',
    style: 'professional',
    description: '专业的商务女性形象，适合企业宣传和教育内容',
    previewImage: `${process.env.API_BASE_URL}/uploads/avatars/emma_preview.jpg`,
    previewVideo: `${process.env.API_BASE_URL}/uploads/avatars/emma_preview.mp4`,
    features: {
      hairColor: 'brown',
      eyeColor: 'brown',
      skinTone: 'medium',
      clothing: 'business-suit'
    }
  },
  {
    id: 'david',
    name: '大卫',
    gender: 'male',
    age: 'middle-aged',
    style: 'professional',
    description: '成熟稳重的商务男性形象，适合正式场合和权威内容',
    previewImage: `${process.env.API_BASE_URL}/uploads/avatars/david_preview.jpg`,
    previewVideo: `${process.env.API_BASE_URL}/uploads/avatars/david_preview.mp4`,
    features: {
      hairColor: 'black',
      eyeColor: 'blue',
      skinTone: 'light',
      clothing: 'business-suit'
    }
  },
  {
    id: 'sophia',
    name: '索菲亚',
    gender: 'female',
    age: 'young-adult',
    style: 'casual',
    description: '年轻活泼的女性形象，适合生活方式和娱乐内容',
    previewImage: `${process.env.API_BASE_URL}/uploads/avatars/sophia_preview.jpg`,
    previewVideo: `${process.env.API_BASE_URL}/uploads/avatars/sophia_preview.mp4`,
    features: {
      hairColor: 'blonde',
      eyeColor: 'green',
      skinTone: 'light',
      clothing: 'casual-wear'
    }
  },
  {
    id: 'alex',
    name: '亚历克斯',
    gender: 'male',
    age: 'young-adult',
    style: 'creative',
    description: '富有创意的年轻男性形象，适合科技和创新内容',
    previewImage: `${process.env.API_BASE_URL}/uploads/avatars/alex_preview.jpg`,
    previewVideo: `${process.env.API_BASE_URL}/uploads/avatars/alex_preview.mp4`,
    features: {
      hairColor: 'brown',
      eyeColor: 'hazel',
      skinTone: 'medium',
      clothing: 'smart-casual'
    }
  }
];

// 获取可用的数字人列表
const getAvatarList = async (req, res) => {
  try {
    res.json({
      success: true,
      avatars: availableAvatars
    });
  } catch (error) {
    console.error('Error getting avatar list:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get avatar list',
      error: error.message
    });
  }
};

// 为镜头配置数字人
const configureAvatar = async (req, res) => {
  try {
    const { 
      shotId, 
      avatarId, 
      position = 'center',
      size = 'medium',
      animation = 'talking',
      background = 'transparent'
    } = req.body;

    if (!shotId || !avatarId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters: shotId, avatarId'
      });
    }

    // 验证数字人ID
    const avatar = availableAvatars.find(a => a.id === avatarId);
    if (!avatar) {
      return res.status(400).json({
        success: false,
        message: 'Invalid avatar ID'
      });
    }

    // 模拟配置处理时间
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const configuration = {
      id: nanoid(),
      shotId,
      avatarId,
      avatarName: avatar.name,
      position,
      size,
      animation,
      background,
      previewUrl: `${process.env.API_BASE_URL}/uploads/avatar-configs/${shotId}_${avatarId}_preview.jpg`,
      status: 'configured',
      createdAt: new Date().toISOString()
    };

    console.log(`Configured avatar ${avatar.name} for shot ${shotId}`);

    res.json({
      success: true,
      configuration
    });

  } catch (error) {
    console.error('Error configuring avatar:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to configure avatar',
      error: error.message
    });
  }
};

// 生成带数字人的视频
const generateAvatarVideo = async (req, res) => {
  try {
    const { 
      shotId,
      avatarId,
      audioUrl,
      script,
      configuration = {},
      duration = 10
    } = req.body;

    if (!shotId || !avatarId || !script) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters: shotId, avatarId, script'
      });
    }

    // 验证数字人ID
    const avatar = availableAvatars.find(a => a.id === avatarId);
    if (!avatar) {
      return res.status(400).json({
        success: false,
        message: 'Invalid avatar ID'
      });
    }

    // 模拟数字人视频生成时间
    const generationTime = Math.max(5000, duration * 800 + Math.random() * 5000);
    await new Promise(resolve => setTimeout(resolve, generationTime));

    const avatarVideo = {
      id: nanoid(),
      shotId,
      avatarId,
      avatarName: avatar.name,
      script,
      audioUrl,
      duration,
      configuration,
      videoUrl: `${process.env.API_BASE_URL}/uploads/avatar-videos/${shotId}_${avatarId}.mp4`,
      thumbnailUrl: `${process.env.API_BASE_URL}/uploads/avatar-videos/${shotId}_${avatarId}_thumb.jpg`,
      status: 'completed',
      fileSize: Math.ceil(duration * 3), // 估算文件大小
      createdAt: new Date().toISOString()
    };

    console.log(`Generated avatar video for shot ${shotId} with ${avatar.name}`);

    res.json({
      success: true,
      video: avatarVideo
    });

  } catch (error) {
    console.error('Error generating avatar video:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate avatar video',
      error: error.message
    });
  }
};

// 获取数字人预览
const getAvatarPreview = async (req, res) => {
  try {
    const { avatarId } = req.params;

    const avatar = availableAvatars.find(a => a.id === avatarId);
    if (!avatar) {
      return res.status(404).json({
        success: false,
        message: 'Avatar not found'
      });
    }

    // 生成预览内容
    const preview = {
      ...avatar,
      sampleText: "您好，我是您的数字人助手。我可以为您的视频内容提供专业的演示和讲解。",
      sampleVideoUrl: `${process.env.API_BASE_URL}/uploads/avatar-samples/${avatarId}_sample.mp4`,
      animations: [
        { id: 'talking', name: '说话', description: '自然的说话动作和表情' },
        { id: 'presenting', name: '演示', description: '适合演示和讲解的手势' },
        { id: 'greeting', name: '问候', description: '友好的问候手势' },
        { id: 'explaining', name: '解释', description: '详细解释时的动作' }
      ]
    };

    res.json({
      success: true,
      preview
    });

  } catch (error) {
    console.error('Error getting avatar preview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get avatar preview',
      error: error.message
    });
  }
};

// 自定义数字人外观
const customizeAvatar = async (req, res) => {
  try {
    const { 
      baseAvatarId,
      customizations = {},
      name
    } = req.body;

    if (!baseAvatarId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameter: baseAvatarId'
      });
    }

    // 验证基础数字人ID
    const baseAvatar = availableAvatars.find(a => a.id === baseAvatarId);
    if (!baseAvatar) {
      return res.status(400).json({
        success: false,
        message: 'Invalid base avatar ID'
      });
    }

    // 模拟自定义处理时间
    await new Promise(resolve => setTimeout(resolve, 3000 + Math.random() * 4000));

    const customAvatarId = nanoid();
    const customAvatar = {
      id: customAvatarId,
      name: name || `自定义${baseAvatar.name}`,
      baseAvatarId,
      gender: baseAvatar.gender,
      age: baseAvatar.age,
      style: customizations.style || baseAvatar.style,
      description: `基于${baseAvatar.name}的自定义数字人`,
      previewImage: `${process.env.API_BASE_URL}/uploads/custom-avatars/${customAvatarId}_preview.jpg`,
      previewVideo: `${process.env.API_BASE_URL}/uploads/custom-avatars/${customAvatarId}_preview.mp4`,
      features: {
        ...baseAvatar.features,
        ...customizations
      },
      isCustom: true,
      createdAt: new Date().toISOString()
    };

    console.log(`Created custom avatar based on ${baseAvatar.name}`);

    res.json({
      success: true,
      avatar: customAvatar
    });

  } catch (error) {
    console.error('Error customizing avatar:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to customize avatar',
      error: error.message
    });
  }
};

module.exports = {
  getAvatarList,
  configureAvatar,
  generateAvatarVideo,
  getAvatarPreview,
  customizeAvatar,
  availableAvatars
};
