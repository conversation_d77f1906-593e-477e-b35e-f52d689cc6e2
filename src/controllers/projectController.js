const { nanoid } = require('nanoid');
const { projects } = require('../db');

const createProject = (req, res) => {
  const {
    name = '未命名项目',
    theme = '',
    requirements = '',
    isDraft = false
  } = req.body;
  const projectId = nanoid();

  const newProject = {
    id: projectId,
    name,
    theme,
    requirements,
    isDraft,
    script: '',
    scenes: [],
    settings: {
      avatarId: null,
      voiceId: null,
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  projects[projectId] = newProject;
  console.log(`Project created: ${projectId} (${isDraft ? 'draft' : 'full'})`);
  res.status(201).json(newProject);
};

const getAllProjects = (req, res) => {
  const projectList = Object.values(projects)
    .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
    .slice(0, 10); // 只返回最近的10个项目

  res.json(projectList);
};

const getProjectById = (req, res) => {
  const { projectId } = req.params;
  const project = projects[projectId];

  if (project) {
    res.json(project);
  } else {
    res.status(404).json({ message: 'Project not found' });
  }
};

const updateProject = (req, res) => {
  const { projectId } = req.params;

  if (!projects[projectId]) {
    return res.status(404).json({ message: 'Project not found' });
  }

  const updatedProject = {
    ...projects[projectId],
    ...req.body,
    id: projectId,
    updatedAt: new Date().toISOString(),
  };

  projects[projectId] = updatedProject;
  console.log(`Project updated: ${projectId}`);
  res.json(updatedProject);
};

const deleteProject = (req, res) => {
  const { projectId } = req.params;

  if (!projects[projectId]) {
    return res.status(404).json({ message: 'Project not found' });
  }

  delete projects[projectId];
  console.log(`Project deleted: ${projectId}`);
  res.json({ success: true, message: 'Project deleted successfully' });
};

const uploadAsset = (req, res) => {
    const { projectId } = req.params;
    if (!projects[projectId]) {
        return res.status(404).json({ message: 'Project not found' });
    }

    if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded.' });
    }

    const assetUrl = `/uploads/${req.file.filename}`;
    console.log(`Asset uploaded for project ${projectId}: ${assetUrl}`);
    
    res.json({ 
        assetId: req.file.filename,
        url: assetUrl 
    });
};

module.exports = {
  createProject,
  getAllProjects,
  getProjectById,
  updateProject,
  deleteProject,
  uploadAsset,
};
