const avatars = [
  {
    id: 'emma',
    name: '艾玛',
    gender: 'female',
    style: 'professional',
    description: '专业的商务女性形象',
    previewUrl: `${process.env.API_BASE_URL}/uploads/avatars/emma_preview.jpg`,
    previewVideo: `${process.env.API_BASE_URL}/uploads/avatars/emma_preview.mp4`
  },
  {
    id: 'david',
    name: '大卫',
    gender: 'male',
    style: 'professional',
    description: '成熟稳重的商务男性形象',
    previewUrl: `${process.env.API_BASE_URL}/uploads/avatars/david_preview.jpg`,
    previewVideo: `${process.env.API_BASE_URL}/uploads/avatars/david_preview.mp4`
  },
  {
    id: 'sophia',
    name: '索菲亚',
    gender: 'female',
    style: 'casual',
    description: '年轻活泼的女性形象',
    previewUrl: `${process.env.API_BASE_URL}/uploads/avatars/sophia_preview.jpg`,
    previewVideo: `${process.env.API_BASE_URL}/uploads/avatars/sophia_preview.mp4`
  },
  {
    id: 'alex',
    name: '亚历克斯',
    gender: 'male',
    style: 'creative',
    description: '富有创意的年轻男性形象',
    previewUrl: `${process.env.API_BASE_URL}/uploads/avatars/alex_preview.jpg`,
    previewVideo: `${process.env.API_BASE_URL}/uploads/avatars/alex_preview.mp4`
  }
];

const voices = [
  {
    id: 'professional-female',
    name: '专业女声',
    lang: 'zh-CN',
    gender: 'female',
    style: 'professional',
    description: '清晰专业的女性声音，适合商务和教育内容',
    previewUrl: `${process.env.API_BASE_URL}/uploads/voices/professional_female.mp3`
  },
  {
    id: 'professional-male',
    name: '专业男声',
    lang: 'zh-CN',
    gender: 'male',
    style: 'professional',
    description: '沉稳专业的男性声音，适合正式场合',
    previewUrl: `${process.env.API_BASE_URL}/uploads/voices/professional_male.mp3`
  },
  {
    id: 'friendly-female',
    name: '亲和女声',
    lang: 'zh-CN',
    gender: 'female',
    style: 'friendly',
    description: '温暖亲和的女性声音，适合生活和娱乐内容',
    previewUrl: `${process.env.API_BASE_URL}/uploads/voices/friendly_female.mp3`
  },
  {
    id: 'energetic-male',
    name: '活力男声',
    lang: 'zh-CN',
    gender: 'male',
    style: 'energetic',
    description: '充满活力的男性声音，适合运动和激励内容',
    previewUrl: `${process.env.API_BASE_URL}/uploads/voices/energetic_male.mp3`
  }
];

const getAvatars = (req, res) => {
  res.json(avatars);
};

const getVoices = (req, res) => {
  res.json(voices);
};

module.exports = {
  getAvatars,
  getVoices,
};