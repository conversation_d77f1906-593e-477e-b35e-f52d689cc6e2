const { nanoid } = require('nanoid');

// 模拟AI脚本生成
const generateScripts = async (req, res) => {
  try {
    const { projectName, projectTheme, projectRequirements, materialSource, uploadedFiles, textMaterial } = req.body;

    // 模拟AI处理时间
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // 根据不同的素材来源生成不同的脚本
    let scripts = [];
    
    if (materialSource === 'upload' && uploadedFiles && uploadedFiles.length > 0) {
      // 基于上传文件生成脚本
      scripts = [
        {
          id: nanoid(),
          text: `开场：展示${projectName}的核心理念，通过精心设计的视觉元素吸引观众注意力。`,
          duration: 8,
          createdAt: new Date().toISOString()
        },
        {
          id: nanoid(),
          text: `主体内容：深入解析项目特色，结合上传的素材文件，展现专业性和创新性。`,
          duration: 15,
          createdAt: new Date().toISOString()
        },
        {
          id: nanoid(),
          text: `结尾：总结要点，呼吁行动，留下深刻印象，促进观众进一步了解。`,
          duration: 7,
          createdAt: new Date().toISOString()
        }
      ];
    } else if (materialSource === 'text' && textMaterial) {
      // 基于文本素材生成脚本
      const textLength = textMaterial.length;
      if (textLength > 500) {
        scripts = [
          {
            id: nanoid(),
            text: `引言：基于您提供的详细文本内容，我们将为您呈现一个完整的故事。`,
            duration: 6,
            createdAt: new Date().toISOString()
          },
          {
            id: nanoid(),
            text: `核心内容：深度解读文本中的关键信息，通过视觉化方式传达核心观点。`,
            duration: 20,
            createdAt: new Date().toISOString()
          },
          {
            id: nanoid(),
            text: `拓展讨论：结合文本背景，提供更深层次的分析和见解。`,
            duration: 12,
            createdAt: new Date().toISOString()
          },
          {
            id: nanoid(),
            text: `总结：回顾要点，强化记忆，为观众提供可行的后续行动建议。`,
            duration: 8,
            createdAt: new Date().toISOString()
          }
        ];
      } else {
        scripts = [
          {
            id: nanoid(),
            text: `开篇：简洁明了地介绍主题，快速抓住观众兴趣点。`,
            duration: 5,
            createdAt: new Date().toISOString()
          },
          {
            id: nanoid(),
            text: `展开：基于您的文本内容，详细阐述核心观点和关键信息。`,
            duration: 12,
            createdAt: new Date().toISOString()
          },
          {
            id: nanoid(),
            text: `收尾：简洁有力的总结，留下深刻印象。`,
            duration: 6,
            createdAt: new Date().toISOString()
          }
        ];
      }
    } else {
      // 默认脚本
      scripts = [
        {
          id: nanoid(),
          text: `清晨的阳光透过百叶窗，洒在整洁的办公桌上。一台笔记本电脑屏幕亮着，显示着${projectName || '公司'}的Logo。`,
          duration: 8,
          createdAt: new Date().toISOString()
        },
        {
          id: nanoid(),
          text: `特写：手指轻快地在键盘上敲击，屏幕上代码飞速滚动，展现专业的工作状态。`,
          duration: 10,
          createdAt: new Date().toISOString()
        },
        {
          id: nanoid(),
          text: `镜头切换至会议室，团队成员围坐在一起，激烈地讨论着。白板上写满了各种想法和流程图。`,
          duration: 12,
          createdAt: new Date().toISOString()
        }
      ];
    }

    console.log(`Generated ${scripts.length} scripts for project: ${projectName}`);
    
    res.json({
      success: true,
      scripts,
      totalDuration: scripts.reduce((sum, script) => sum + script.duration, 0)
    });

  } catch (error) {
    console.error('Error generating scripts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate scripts',
      error: error.message
    });
  }
};

// 重新生成特定脚本
const regenerateScript = async (req, res) => {
  try {
    const { scriptId, context } = req.body;

    // 模拟AI重新生成
    await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 1200));

    const regeneratedScript = {
      id: scriptId,
      text: `重新生成：${context ? '基于上下文，' : ''}创新的视角展现内容，通过不同的叙述方式带来全新体验。`,
      duration: 8 + Math.floor(Math.random() * 10),
      createdAt: new Date().toISOString(),
      regenerated: true
    };

    console.log(`Regenerated script: ${scriptId}`);
    
    res.json({
      success: true,
      script: regeneratedScript
    });

  } catch (error) {
    console.error('Error regenerating script:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to regenerate script',
      error: error.message
    });
  }
};

// 更新脚本
const updateScript = async (req, res) => {
  try {
    const { scriptId } = req.params;
    const { text, duration } = req.body;

    const updatedScript = {
      id: scriptId,
      text,
      duration: duration || 10,
      updatedAt: new Date().toISOString()
    };

    console.log(`Updated script: ${scriptId}`);
    
    res.json({
      success: true,
      script: updatedScript
    });

  } catch (error) {
    console.error('Error updating script:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update script',
      error: error.message
    });
  }
};

// 删除脚本
const deleteScript = async (req, res) => {
  try {
    const { scriptId } = req.params;

    console.log(`Deleted script: ${scriptId}`);
    
    res.json({
      success: true,
      message: 'Script deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting script:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete script',
      error: error.message
    });
  }
};

// 生成镜头
const generateShots = async (req, res) => {
  try {
    const { scripts } = req.body;

    // 模拟AI处理时间
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2500));

    // 基于前端优先原则，返回前端期望的数据格式
    const shots = scripts.map((script, index) => ({
      id: script.id, // 使用脚本ID作为镜头ID，保持一致性
      number: index + 1,
      scriptText: script.text,
      imageUrl: `https://picsum.photos/seed/${Math.floor(Math.random() * 1000)}/400/225`,
      // 保留后端生成的额外信息，但不影响前端核心功能
      duration: script.duration,
      style: 'cinematic',
      cameraAngle: ['wide', 'medium', 'close-up', 'extreme-close-up'][Math.floor(Math.random() * 4)],
      lighting: ['natural', 'soft', 'dramatic', 'bright'][Math.floor(Math.random() * 4)],
      mood: ['professional', 'energetic', 'calm', 'inspiring'][Math.floor(Math.random() * 4)],
      createdAt: new Date().toISOString()
    }));

    console.log(`Generated ${shots.length} shots from ${scripts.length} scripts`);

    res.json({
      success: true,
      shots,
      totalDuration: shots.reduce((sum, shot) => sum + (shot.duration || 0), 0)
    });

  } catch (error) {
    console.error('Error generating shots:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate shots',
      error: error.message
    });
  }
};

// 重新生成特定镜头
const regenerateShot = async (req, res) => {
  try {
    const { shotId, scriptText } = req.body;

    // 模拟AI重新生成
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1500));

    // 基于前端优先原则，返回前端期望的数据格式
    const regeneratedShot = {
      id: shotId,
      scriptText: `全新视角：${scriptText}`,
      imageUrl: `https://picsum.photos/seed/${Math.floor(Math.random() * 1000)}/400/225`,
      // 保留后端生成的额外信息
      duration: 8 + Math.floor(Math.random() * 12),
      style: ['cinematic', 'documentary', 'artistic', 'commercial'][Math.floor(Math.random() * 4)],
      cameraAngle: ['wide', 'medium', 'close-up', 'extreme-close-up'][Math.floor(Math.random() * 4)],
      lighting: ['natural', 'soft', 'dramatic', 'bright'][Math.floor(Math.random() * 4)],
      mood: ['professional', 'energetic', 'calm', 'inspiring'][Math.floor(Math.random() * 4)],
      createdAt: new Date().toISOString(),
      regenerated: true
    };

    console.log(`Regenerated shot: ${shotId}`);

    res.json({
      success: true,
      shot: regeneratedShot
    });

  } catch (error) {
    console.error('Error regenerating shot:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to regenerate shot',
      error: error.message
    });
  }
};

module.exports = {
  generateScripts,
  regenerateScript,
  updateScript,
  deleteScript,
  generateShots,
  regenerateShot
};
