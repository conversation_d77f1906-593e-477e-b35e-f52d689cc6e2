const { nanoid } = require('nanoid');
const { tasks, projects } = require('../db');

const createTask = (req, res) => {
    const {
      projectId,
      taskType = 'video-render',
      taskData = {}
    } = req.body;

    if (projectId && !projects[projectId]) {
      return res.status(404).json({ message: 'Project not found' });
    }

    const taskId = `task_${nanoid()}`;
    const newTask = {
      id: taskId,
      projectId,
      taskType,
      taskData,
      status: 'processing',
      progress: 0,
      createdAt: new Date().toISOString(),
      estimatedDuration: getEstimatedDuration(taskType, taskData)
    };
    tasks[taskId] = newTask;

    // Start task processing based on type
    processTask(taskId, taskType, taskData);

    console.log(`Started ${taskType} task: ${taskId}${projectId ? ` for project: ${projectId}` : ''}`);

    res.status(202).json({
      taskId,
      estimatedDuration: newTask.estimatedDuration,
      status: 'processing'
    });
};

// Get estimated duration based on task type
const getEstimatedDuration = (taskType, taskData) => {
  switch (taskType) {
    case 'script-generation':
      return 5000; // 5 seconds
    case 'voice-generation':
      return taskData.shots ? taskData.shots.length * 3000 : 10000; // 3s per shot
    case 'video-generation':
      return taskData.shots ? taskData.shots.length * 5000 : 15000; // 5s per shot
    case 'avatar-configuration':
      return taskData.shots ? taskData.shots.length * 2000 : 8000; // 2s per shot
    case 'video-composition':
      return taskData.totalDuration ? taskData.totalDuration * 1000 : 20000; // 1s per second of video
    case 'video-render':
    default:
      return 30000; // 30 seconds
  }
};

// Process task based on type
const processTask = (taskId, taskType, taskData) => {
  const task = tasks[taskId];
  if (!task) return;

  const estimatedDuration = task.estimatedDuration;
  const updateInterval = Math.max(500, estimatedDuration / 20); // Update 20 times during processing

  let progress = 0;
  const interval = setInterval(() => {
    progress += 5;

    if (tasks[taskId]) {
      tasks[taskId].progress = Math.min(progress, 95); // Keep at 95% until completion
      tasks[taskId].updatedAt = new Date().toISOString();

      // Add some random variation to make it feel more realistic
      if (Math.random() < 0.1) {
        tasks[taskId].progress = Math.max(0, tasks[taskId].progress - 2);
      }

      if (progress >= 100) {
        clearInterval(interval);
        completeTask(taskId, taskType, taskData);
      }
    } else {
      clearInterval(interval);
    }
  }, updateInterval);
};

// Complete task and set result
const completeTask = (taskId, taskType, taskData) => {
  if (!tasks[taskId]) return;

  tasks[taskId].status = 'completed';
  tasks[taskId].progress = 100;
  tasks[taskId].completedAt = new Date().toISOString();

  // Set result based on task type
  switch (taskType) {
    case 'script-generation':
      tasks[taskId].result = {
        scripts: generateMockScripts(taskData),
        message: 'Scripts generated successfully'
      };
      break;
    case 'voice-generation':
      tasks[taskId].result = {
        audioFiles: generateMockAudioFiles(taskData),
        message: 'Voice generation completed'
      };
      break;
    case 'video-generation':
      tasks[taskId].result = {
        videoFiles: generateMockVideoFiles(taskData),
        message: 'Video generation completed'
      };
      break;
    case 'avatar-configuration':
      tasks[taskId].result = {
        configurations: generateMockAvatarConfigs(taskData),
        message: 'Avatar configuration completed'
      };
      break;
    case 'video-composition':
      tasks[taskId].result = {
        finalVideo: {
          url: `${process.env.API_BASE_URL}/uploads/composed/${taskId}_final.mp4`,
          duration: taskData.totalDuration || 60,
          fileSize: Math.ceil((taskData.totalDuration || 60) * 5)
        },
        message: 'Video composition completed'
      };
      break;
    case 'video-render':
    default:
      tasks[taskId].result = {
        videoUrl: `${process.env.API_BASE_URL}/uploads/rendered/${taskId}_final.mp4`,
        thumbnailUrl: `${process.env.API_BASE_URL}/uploads/rendered/${taskId}_thumb.jpg`,
        message: 'Video rendering completed'
      };
      break;
  }

  console.log(`Task ${taskId} (${taskType}) completed successfully`);
};

// Mock data generators
const generateMockScripts = (taskData) => {
  const count = taskData.scriptCount || 3;
  return Array.from({ length: count }, (_, i) => ({
    id: nanoid(),
    text: `Generated script ${i + 1}: This is a sample script content based on your requirements.`,
    duration: 8 + Math.floor(Math.random() * 10),
    createdAt: new Date().toISOString()
  }));
};

const generateMockAudioFiles = (taskData) => {
  const shots = taskData.shots || [];
  return shots.map(shot => ({
    id: nanoid(),
    shotId: shot.id,
    audioUrl: `${process.env.API_BASE_URL}/uploads/audio/${shot.id}_voice.mp3`,
    duration: shot.duration || 10,
    createdAt: new Date().toISOString()
  }));
};

const generateMockVideoFiles = (taskData) => {
  const shots = taskData.shots || [];
  return shots.map(shot => ({
    id: nanoid(),
    shotId: shot.id,
    videoUrl: `${process.env.API_BASE_URL}/uploads/videos/${shot.id}_video.mp4`,
    thumbnailUrl: `${process.env.API_BASE_URL}/uploads/videos/${shot.id}_thumb.jpg`,
    duration: shot.duration || 10,
    createdAt: new Date().toISOString()
  }));
};

const generateMockAvatarConfigs = (taskData) => {
  const shots = taskData.shots || [];
  return shots.map(shot => ({
    id: nanoid(),
    shotId: shot.id,
    avatarId: taskData.avatarId || 'emma',
    previewUrl: `${process.env.API_BASE_URL}/uploads/avatar-configs/${shot.id}_preview.jpg`,
    createdAt: new Date().toISOString()
  }));
};

const getTaskById = (req, res) => {
    const { taskId } = req.params;
    const task = tasks[taskId];

    if (task) {
      res.json({
        success: true,
        task
      });
    } else {
      res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }
};

// Get all tasks for a project
const getProjectTasks = (req, res) => {
  const { projectId } = req.params;

  const projectTasks = Object.values(tasks).filter(task => task.projectId === projectId);

  res.json({
    success: true,
    tasks: projectTasks
  });
};

// Cancel a task
const cancelTask = (req, res) => {
  const { taskId } = req.params;

  if (tasks[taskId]) {
    tasks[taskId].status = 'cancelled';
    tasks[taskId].cancelledAt = new Date().toISOString();

    res.json({
      success: true,
      message: 'Task cancelled successfully'
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'Task not found'
    });
  }
};

module.exports = {
    createTask,
    getTaskById,
    getProjectTasks,
    cancelTask
};