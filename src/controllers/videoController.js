const { nanoid } = require('nanoid');

// 可用的视频风格
const availableStyles = [
  {
    id: 'cinematic',
    name: '电影风格',
    description: '专业的电影级画面质量，适合高端内容'
  },
  {
    id: 'documentary',
    name: '纪录片风格',
    description: '真实自然的画面风格，适合教育和解说内容'
  },
  {
    id: 'commercial',
    name: '商业广告风格',
    description: '明亮活泼的商业化风格，适合产品展示'
  },
  {
    id: 'artistic',
    name: '艺术风格',
    description: '富有创意的艺术化表现，适合品牌宣传'
  }
];

// 生成单个视频
const generateVideo = async (req, res) => {
  try {
    const { 
      shotId, 
      description, 
      style = 'cinematic', 
      duration = 10,
      resolution = '1080p',
      aspectRatio = '16:9'
    } = req.body;

    if (!shotId || !description) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters: shotId, description'
      });
    }

    // 验证风格
    const videoStyle = availableStyles.find(s => s.id === style);
    if (!videoStyle) {
      return res.status(400).json({
        success: false,
        message: 'Invalid video style'
      });
    }

    // 模拟视频生成时间（根据时长调整）
    const generationTime = Math.max(3000, duration * 500 + Math.random() * 3000);
    await new Promise(resolve => setTimeout(resolve, generationTime));

    const videoResult = {
      id: nanoid(),
      shotId,
      description,
      style,
      styleName: videoStyle.name,
      duration,
      resolution,
      aspectRatio,
      videoUrl: `${process.env.API_BASE_URL}/uploads/videos/${shotId}_${style}.mp4`,
      thumbnailUrl: `${process.env.API_BASE_URL}/uploads/thumbnails/${shotId}_${style}.jpg`,
      status: 'completed',
      fileSize: Math.ceil(duration * 2.5), // 估算文件大小 (MB)
      createdAt: new Date().toISOString()
    };

    console.log(`Generated video for shot ${shotId} in ${videoStyle.name} style`);

    res.json({
      success: true,
      video: videoResult
    });

  } catch (error) {
    console.error('Error generating video:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate video',
      error: error.message
    });
  }
};

// 批量生成视频
const generateBatchVideo = async (req, res) => {
  try {
    const { 
      shots, 
      style = 'cinematic', 
      resolution = '1080p',
      aspectRatio = '16:9'
    } = req.body;

    if (!shots || !Array.isArray(shots) || shots.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid shots array'
      });
    }

    // 验证风格
    const videoStyle = availableStyles.find(s => s.id === style);
    if (!videoStyle) {
      return res.status(400).json({
        success: false,
        message: 'Invalid video style'
      });
    }

    // 创建任务ID用于跟踪进度
    const taskId = nanoid();

    // 模拟批量处理时间
    const totalDuration = shots.reduce((sum, shot) => sum + (shot.duration || 10), 0);
    const generationTime = Math.max(5000, totalDuration * 300 + Math.random() * 5000);
    
    // 立即返回任务ID，实际处理在后台进行
    setTimeout(async () => {
      // 这里应该是实际的视频生成逻辑
      console.log(`Batch video generation completed for task ${taskId}`);
    }, generationTime);

    const videoResults = shots.map(shot => ({
      id: nanoid(),
      shotId: shot.id,
      description: shot.description,
      style,
      styleName: videoStyle.name,
      duration: shot.duration || 10,
      resolution,
      aspectRatio,
      videoUrl: `${process.env.API_BASE_URL}/uploads/videos/${shot.id}_${style}.mp4`,
      thumbnailUrl: `${process.env.API_BASE_URL}/uploads/thumbnails/${shot.id}_${style}.jpg`,
      status: 'processing',
      fileSize: Math.ceil((shot.duration || 10) * 2.5),
      createdAt: new Date().toISOString()
    }));

    console.log(`Started batch video generation for ${shots.length} shots (Task: ${taskId})`);

    res.json({
      success: true,
      taskId,
      videos: videoResults,
      estimatedCompletionTime: new Date(Date.now() + generationTime).toISOString()
    });

  } catch (error) {
    console.error('Error generating batch video:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate batch video',
      error: error.message
    });
  }
};

// 合成最终视频
const composeVideo = async (req, res) => {
  try {
    const { 
      projectId,
      shots,
      transitions = 'fade',
      backgroundMusic,
      outputSettings = {
        resolution: '1080p',
        format: 'MP4',
        quality: 'high'
      }
    } = req.body;

    if (!projectId || !shots || !Array.isArray(shots)) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters: projectId, shots'
      });
    }

    // 计算总时长
    const totalDuration = shots.reduce((sum, shot) => sum + (shot.duration || 10), 0);
    
    // 模拟视频合成时间
    const compositionTime = Math.max(8000, totalDuration * 1000 + Math.random() * 10000);
    await new Promise(resolve => setTimeout(resolve, compositionTime));

    const composedVideo = {
      id: nanoid(),
      projectId,
      totalDuration,
      shotCount: shots.length,
      transitions,
      backgroundMusic: backgroundMusic || null,
      outputSettings,
      videoUrl: `${process.env.API_BASE_URL}/uploads/composed/${projectId}_final.mp4`,
      thumbnailUrl: `${process.env.API_BASE_URL}/uploads/composed/${projectId}_thumbnail.jpg`,
      fileSize: Math.ceil(totalDuration * 5), // 估算最终文件大小
      status: 'completed',
      createdAt: new Date().toISOString()
    };

    console.log(`Composed final video for project ${projectId} (${totalDuration}s, ${shots.length} shots)`);

    res.json({
      success: true,
      video: composedVideo
    });

  } catch (error) {
    console.error('Error composing video:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to compose video',
      error: error.message
    });
  }
};

// 获取视频生成状态
const getVideoStatus = async (req, res) => {
  try {
    const { taskId } = req.params;

    // 模拟状态查询
    const statuses = ['processing', 'completed', 'failed'];
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
    
    const statusInfo = {
      taskId,
      status: randomStatus,
      progress: randomStatus === 'processing' ? Math.floor(Math.random() * 100) : 100,
      message: randomStatus === 'failed' ? 'Generation failed due to technical issues' : 'Processing...',
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      status: statusInfo
    });

  } catch (error) {
    console.error('Error getting video status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get video status',
      error: error.message
    });
  }
};

// 导出视频
const exportVideo = async (req, res) => {
  try {
    const { 
      videoId,
      format = 'MP4',
      quality = 'high',
      resolution = '1080p'
    } = req.body;

    if (!videoId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameter: videoId'
      });
    }

    // 模拟导出处理时间
    await new Promise(resolve => setTimeout(resolve, 3000 + Math.random() * 5000));

    const exportResult = {
      id: nanoid(),
      videoId,
      format,
      quality,
      resolution,
      downloadUrl: `${process.env.API_BASE_URL}/uploads/exports/${videoId}_${quality}.${format.toLowerCase()}`,
      fileSize: Math.ceil(Math.random() * 100 + 50), // 随机文件大小
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24小时后过期
      createdAt: new Date().toISOString()
    };

    console.log(`Exported video ${videoId} as ${format} (${quality} quality)`);

    res.json({
      success: true,
      export: exportResult
    });

  } catch (error) {
    console.error('Error exporting video:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export video',
      error: error.message
    });
  }
};

module.exports = {
  generateVideo,
  generateBatchVideo,
  composeVideo,
  getVideoStatus,
  exportVideo,
  availableStyles
};
