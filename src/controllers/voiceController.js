const { nanoid } = require('nanoid');

// 可用的声音类型
const availableVoices = [
  {
    id: 'professional-female',
    name: '专业女声',
    language: 'zh-CN',
    gender: 'female',
    style: 'professional',
    description: '清晰专业的女性声音，适合商务和教育内容'
  },
  {
    id: 'professional-male',
    name: '专业男声',
    language: 'zh-CN',
    gender: 'male',
    style: 'professional',
    description: '沉稳专业的男性声音，适合正式场合'
  },
  {
    id: 'friendly-female',
    name: '亲和女声',
    language: 'zh-CN',
    gender: 'female',
    style: 'friendly',
    description: '温暖亲和的女性声音，适合生活和娱乐内容'
  },
  {
    id: 'energetic-male',
    name: '活力男声',
    language: 'zh-CN',
    gender: 'male',
    style: 'energetic',
    description: '充满活力的男性声音，适合运动和激励内容'
  }
];

// 生成单个镜头的配音
const generateVoice = async (req, res) => {
  try {
    const { shotId, text, voiceId, speed = 1.0, pitch = 1.0 } = req.body;

    if (!shotId || !text || !voiceId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters: shotId, text, voiceId'
      });
    }

    // 验证声音ID
    const voice = availableVoices.find(v => v.id === voiceId);
    if (!voice) {
      return res.status(400).json({
        success: false,
        message: 'Invalid voice ID'
      });
    }

    // 模拟配音生成时间
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

    // 计算预估时长（基于文本长度）
    const estimatedDuration = Math.max(3, Math.ceil(text.length / 8)); // 大约每秒8个字符

    const voiceResult = {
      id: nanoid(),
      shotId,
      text,
      voiceId,
      voiceName: voice.name,
      audioUrl: `${process.env.API_BASE_URL}/uploads/audio/${shotId}_${voiceId}.mp3`,
      duration: estimatedDuration,
      speed,
      pitch,
      status: 'completed',
      createdAt: new Date().toISOString()
    };

    console.log(`Generated voice for shot ${shotId} using ${voice.name}`);

    res.json({
      success: true,
      voice: voiceResult
    });

  } catch (error) {
    console.error('Error generating voice:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate voice',
      error: error.message
    });
  }
};

// 批量生成配音
const generateBatchVoice = async (req, res) => {
  try {
    const { shots, voiceId, speed = 1.0, pitch = 1.0 } = req.body;

    if (!shots || !Array.isArray(shots) || shots.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid shots array'
      });
    }

    // 验证声音ID
    const voice = availableVoices.find(v => v.id === voiceId);
    if (!voice) {
      return res.status(400).json({
        success: false,
        message: 'Invalid voice ID'
      });
    }

    // 模拟批量处理时间
    await new Promise(resolve => setTimeout(resolve, 3000 + Math.random() * 5000));

    const voiceResults = shots.map(shot => {
      const estimatedDuration = Math.max(3, Math.ceil(shot.description.length / 8));
      
      return {
        id: nanoid(),
        shotId: shot.id,
        text: shot.description,
        voiceId,
        voiceName: voice.name,
        audioUrl: `${process.env.API_BASE_URL}/uploads/audio/${shot.id}_${voiceId}.mp3`,
        duration: estimatedDuration,
        speed,
        pitch,
        status: 'completed',
        createdAt: new Date().toISOString()
      };
    });

    console.log(`Generated batch voice for ${shots.length} shots using ${voice.name}`);

    res.json({
      success: true,
      voices: voiceResults,
      totalDuration: voiceResults.reduce((sum, voice) => sum + voice.duration, 0)
    });

  } catch (error) {
    console.error('Error generating batch voice:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate batch voice',
      error: error.message
    });
  }
};

// 获取声音预览
const getVoicePreview = async (req, res) => {
  try {
    const { voiceId } = req.params;

    const voice = availableVoices.find(v => v.id === voiceId);
    if (!voice) {
      return res.status(404).json({
        success: false,
        message: 'Voice not found'
      });
    }

    // 模拟预览音频生成
    const previewText = "这是一段示例文本，用于展示这个声音的特色和风格。";
    
    res.json({
      success: true,
      voice: {
        ...voice,
        previewText,
        previewUrl: `${process.env.API_BASE_URL}/uploads/previews/${voiceId}_preview.mp3`,
        duration: 5
      }
    });

  } catch (error) {
    console.error('Error getting voice preview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get voice preview',
      error: error.message
    });
  }
};

// 文本转语音合成
const synthesizeText = async (req, res) => {
  try {
    const { text, voiceId, speed = 1.0, pitch = 1.0, format = 'mp3' } = req.body;

    if (!text || !voiceId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters: text, voiceId'
      });
    }

    // 验证声音ID
    const voice = availableVoices.find(v => v.id === voiceId);
    if (!voice) {
      return res.status(400).json({
        success: false,
        message: 'Invalid voice ID'
      });
    }

    // 模拟合成时间
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2500));

    const audioId = nanoid();
    const estimatedDuration = Math.max(2, Math.ceil(text.length / 8));

    const synthesisResult = {
      id: audioId,
      text,
      voiceId,
      voiceName: voice.name,
      audioUrl: `${process.env.API_BASE_URL}/uploads/synthesis/${audioId}.${format}`,
      duration: estimatedDuration,
      speed,
      pitch,
      format,
      status: 'completed',
      createdAt: new Date().toISOString()
    };

    console.log(`Synthesized text to speech: ${text.substring(0, 50)}...`);

    res.json({
      success: true,
      audio: synthesisResult
    });

  } catch (error) {
    console.error('Error synthesizing text:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to synthesize text',
      error: error.message
    });
  }
};

module.exports = {
  generateVoice,
  generateBatchVoice,
  getVoicePreview,
  synthesizeText,
  availableVoices
};
