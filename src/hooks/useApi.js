import { useCallback } from 'react';
import { useApp } from '../context/AppContext.jsx';
import apiService from '../api/index.js';

export function useApi() {
  const { dispatch, ActionTypes } = useApp();

  // 通用API调用包装器
  const apiCall = useCallback(async (apiFunction, ...args) => {
    dispatch({ type: ActionTypes.SET_LOADING, payload: true });
    dispatch({ type: ActionTypes.SET_ERROR, payload: null });

    try {
      const result = await apiFunction(...args);
      dispatch({ type: ActionTypes.SET_API_CONNECTED, payload: true });
      return result;
    } catch (error) {
      console.error('API call failed:', error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: error.message });
      dispatch({ type: ActionTypes.SET_API_CONNECTED, payload: false });
      throw error;
    } finally {
      dispatch({ type: ActionTypes.SET_LOADING, payload: false });
    }
  }, [dispatch, ActionTypes]);

  // 项目管理
  const createProject = useCallback(async (projectData) => {
    const result = await apiCall(apiService.createProject, projectData);
    if (result.id) {
      dispatch({ type: ActionTypes.SET_PROJECT_ID, payload: result.id });
    }
    return result;
  }, [apiCall, dispatch, ActionTypes]);

  const updateProject = useCallback(async (projectId, projectData) => {
    return await apiCall(apiService.updateProject, projectId, projectData);
  }, [apiCall]);

  const uploadAsset = useCallback(async (projectId, file) => {
    return await apiCall(apiService.uploadAsset, projectId, file);
  }, [apiCall]);

  // 脚本生成
  const generateScripts = useCallback(async (scriptData) => {
    const result = await apiCall(apiService.generateScripts, scriptData);
    if (result.success && result.scripts) {
      // 替换整个脚本数组
      dispatch({ type: ActionTypes.SET_SCRIPTS, payload: result.scripts });
    }
    return result;
  }, [apiCall, dispatch, ActionTypes]);

  const generateShots = useCallback(async (scripts) => {
    const result = await apiCall(apiService.generateShots, scripts);
    if (result.success && result.shots) {
      dispatch({ type: ActionTypes.GENERATE_SHOTS, payload: result.shots });
    }
    return result;
  }, [apiCall, dispatch, ActionTypes]);

  const regenerateShot = useCallback(async (shotId, scriptText) => {
    const result = await apiCall(apiService.regenerateShot, shotId, scriptText);
    if (result.success && result.shot) {
      dispatch({ 
        type: ActionTypes.REGENERATE_SHOT, 
        payload: { id: shotId, newImageUrl: result.shot.imageUrl }
      });
    }
    return result;
  }, [apiCall, dispatch, ActionTypes]);

  // 配音生成
  const generateVoice = useCallback(async (voiceData) => {
    const result = await apiCall(apiService.generateVoice, voiceData);
    if (result.success) {
      dispatch({ type: ActionTypes.GENERATE_VOICE });
    }
    return result;
  }, [apiCall, dispatch, ActionTypes]);

  const generateBatchVoice = useCallback(async (voiceData) => {
    const result = await apiCall(apiService.generateBatchVoice, voiceData);
    if (result.success) {
      dispatch({ type: ActionTypes.GENERATE_VOICE });
    }
    return result;
  }, [apiCall, dispatch, ActionTypes]);

  // 视频生成
  const generateVideo = useCallback(async (videoData) => {
    const result = await apiCall(apiService.generateVideo, videoData);
    if (result.success) {
      dispatch({ type: ActionTypes.GENERATE_VIDEO });
    }
    return result;
  }, [apiCall, dispatch, ActionTypes]);

  const generateBatchVideo = useCallback(async (videoData) => {
    const result = await apiCall(apiService.generateBatchVideo, videoData);
    if (result.success) {
      dispatch({ type: ActionTypes.GENERATE_VIDEO });
      // 如果有任务ID，可以跟踪任务状态
      if (result.taskId) {
        dispatch({ 
          type: ActionTypes.UPDATE_TASK, 
          payload: { id: result.taskId, status: 'processing', type: 'video-generation' }
        });
      }
    }
    return result;
  }, [apiCall, dispatch, ActionTypes]);

  const composeVideo = useCallback(async (compositionData) => {
    const result = await apiCall(apiService.composeVideo, compositionData);
    if (result.success && result.video) {
      dispatch({ 
        type: ActionTypes.COMPOSE_VIDEO, 
        payload: result.video.videoUrl 
      });
    }
    return result;
  }, [apiCall, dispatch, ActionTypes]);

  // 数字人/头像
  const configureAvatar = useCallback(async (avatarData) => {
    const result = await apiCall(apiService.configureAvatar, avatarData);
    if (result.success) {
      dispatch({ type: ActionTypes.CONFIGURE_AVATAR });
    }
    return result;
  }, [apiCall, dispatch, ActionTypes]);

  const generateAvatarVideo = useCallback(async (avatarData) => {
    return await apiCall(apiService.generateAvatarVideo, avatarData);
  }, [apiCall]);

  // 资源管理
  const loadVoices = useCallback(async () => {
    const result = await apiCall(apiService.getVoices);
    // 处理可能的包装格式或直接数组格式
    const voices = Array.isArray(result) ? result : (result.voices || result.data || []);
    dispatch({ type: ActionTypes.SET_AVAILABLE_VOICES, payload: voices });
    return voices;
  }, [apiCall, dispatch, ActionTypes]);

  const loadAvatars = useCallback(async () => {
    const result = await apiCall(apiService.getAvatarList);
    if (result.success && result.avatars) {
      dispatch({ type: ActionTypes.SET_AVAILABLE_AVATARS, payload: result.avatars });
      return result.avatars;
    }
    return [];
  }, [apiCall, dispatch, ActionTypes]);

  // 任务管理
  const getTaskStatus = useCallback(async (taskId) => {
    const result = await apiCall(apiService.getTask, taskId);
    if (result.success && result.task) {
      dispatch({ type: ActionTypes.UPDATE_TASK, payload: result.task });
    }
    return result;
  }, [apiCall, dispatch, ActionTypes]);

  // 健康检查
  const checkApiHealth = useCallback(async () => {
    try {
      await apiService.healthCheck();
      dispatch({ type: ActionTypes.SET_API_CONNECTED, payload: true });
      return true;
    } catch (error) {
      dispatch({ type: ActionTypes.SET_API_CONNECTED, payload: false });
      return false;
    }
  }, [dispatch, ActionTypes]);

  return {
    // 项目管理
    createProject,
    updateProject,
    uploadAsset,
    
    // 脚本生成
    generateScripts,
    generateShots,
    regenerateShot,
    
    // 配音生成
    generateVoice,
    generateBatchVoice,
    
    // 视频生成
    generateVideo,
    generateBatchVideo,
    composeVideo,
    
    // 数字人/头像
    configureAvatar,
    generateAvatarVideo,
    
    // 资源管理
    loadVoices,
    loadAvatars,
    
    // 任务管理
    getTaskStatus,
    
    // 健康检查
    checkApiHealth
  };
}
