import { useApp } from '../context/AppContext.jsx';

export function useNavigation() {
  const { state, dispatch, ActionTypes } = useApp();

  const tabs = [
    { id: 'upload', label: '上传内容', icon: 'cloud_upload' },
    { id: 'script', label: '生成脚本', icon: 'description' },
    { id: 'storyboard', label: '镜头编辑', icon: 'photo_library' },
    { id: 'voice', label: '生成配音', icon: 'record_voice_over' },
    { id: 'video', label: '生成视频', icon: 'videocam' },
    { id: 'avatar', label: '添加数字人', icon: 'person' },
    { id: 'compose', label: '视频合成', icon: 'video_library' },
    { id: 'export', label: '导出视频', icon: 'movie' },
    { id: 'api-test', label: 'API测试', icon: 'bug_report' }
  ];

  const setCurrentTab = (tabId) => {
    dispatch({ type: ActionTypes.SET_CURRENT_TAB, payload: tabId });
    
    // Auto-generate shots when entering storyboard tab
    if (tabId === 'storyboard' && state.shots.length === 0) {
      dispatch({ type: ActionTypes.GENERATE_SHOTS });
    }
  };

  const getCurrentTabIndex = () => {
    return tabs.findIndex(tab => tab.id === state.currentTab);
  };

  const getCompletedSteps = () => {
    const currentIndex = getCurrentTabIndex();
    return tabs.slice(0, currentIndex + 1).map(tab => tab.id);
  };

  const canNavigateToTab = (tabId) => {
    // API测试页面始终可访问
    if (tabId === 'api-test') return true;

    const tabIndex = tabs.findIndex(tab => tab.id === tabId);
    const currentIndex = getCurrentTabIndex();

    // Allow navigation to current tab and previous tabs
    // Allow navigation to next tab only if current requirements are met
    if (tabIndex <= currentIndex) return true;
    if (tabIndex === currentIndex + 1) {
      // Add specific validation logic here if needed
      return true;
    }
    return false;
  };

  return {
    tabs,
    currentTab: state.currentTab,
    setCurrentTab,
    getCurrentTabIndex,
    getCompletedSteps,
    canNavigateToTab
  };
}
