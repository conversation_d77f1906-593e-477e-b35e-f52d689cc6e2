import { useCallback } from 'react';
import { message } from 'antd';

export function useToast() {
  const showSuccess = useCallback((msg, duration = 3) => {
    message.success(msg, duration);
  }, []);

  const showError = useCallback((msg, duration = 3) => {
    message.error(msg, duration);
  }, []);

  const showWarning = useCallback((msg, duration = 3) => {
    message.warning(msg, duration);
  }, []);

  const showInfo = useCallback((msg, duration = 3) => {
    message.info(msg, duration);
  }, []);

  const clearAll = useCallback(() => {
    message.destroy();
  }, []);

  // 为了保持向后兼容，保留原有的接口
  const addToast = useCallback((msg, type = 'info', duration = 3) => {
    switch (type) {
      case 'success':
        return showSuccess(msg, duration);
      case 'error':
        return showError(msg, duration);
      case 'warning':
        return showWarning(msg, duration);
      case 'info':
      default:
        return showInfo(msg, duration);
    }
  }, [showSuccess, showError, showWarning, showInfo]);

  return {
    // 保持向后兼容的接口
    toasts: [], // Ant Design message 不需要状态管理
    addToast,
    removeToast: () => {}, // Ant Design 自动管理
    showSuccess,
    showError,
    showWarning,
    showInfo,
    clearAll
  };
}
