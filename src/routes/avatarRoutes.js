const express = require('express');
const router = express.Router();
const avatarController = require('../controllers/avatarController');

// GET /api/avatar/list - Get available avatars
router.get('/list', avatarController.getAvatarList);

// POST /api/avatar/configure - Configure avatar for shots
router.post('/configure', avatarController.configureAvatar);

// POST /api/avatar/generate - Generate avatar video
router.post('/generate', avatarController.generateAvatarVideo);

// GET /api/avatar/preview/:avatarId - Get avatar preview
router.get('/preview/:avatarId', avatarController.getAvatarPreview);

// POST /api/avatar/customize - Customize avatar appearance
router.post('/customize', avatarController.customizeAvatar);

module.exports = router;
