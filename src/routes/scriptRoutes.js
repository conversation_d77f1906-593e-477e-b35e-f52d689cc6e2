const express = require('express');
const router = express.Router();
const scriptController = require('../controllers/scriptController');

// POST /api/scripts/generate - Generate scripts from project materials
router.post('/generate', scriptController.generateScripts);

// POST /api/scripts/regenerate - Regenerate a specific script
router.post('/regenerate', scriptController.regenerateScript);

// PUT /api/scripts/:scriptId - Update a script
router.put('/:scriptId', scriptController.updateScript);

// DELETE /api/scripts/:scriptId - Delete a script
router.delete('/:scriptId', scriptController.deleteScript);

// POST /api/scripts/shots/generate - Generate shots from scripts
router.post('/shots/generate', scriptController.generateShots);

// POST /api/scripts/shots/regenerate - Regenerate a specific shot
router.post('/shots/regenerate', scriptController.regenerateShot);

module.exports = router;
