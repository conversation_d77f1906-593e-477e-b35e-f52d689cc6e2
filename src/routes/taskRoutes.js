const express = require('express');
const router = express.Router();
const taskController = require('../controllers/taskController');

// POST /api/tasks - Create a new task (generic)
router.post('/', taskController.createTask);

// POST /api/tasks/video-render - Create a video render task (legacy support)
router.post('/video-render', taskController.createTask);

// GET /api/tasks/:taskId - Get a task by ID
router.get('/:taskId', taskController.getTaskById);

// GET /api/tasks/project/:projectId - Get all tasks for a project
router.get('/project/:projectId', taskController.getProjectTasks);

// DELETE /api/tasks/:taskId - Cancel a task
router.delete('/:taskId', taskController.cancelTask);

module.exports = router;
