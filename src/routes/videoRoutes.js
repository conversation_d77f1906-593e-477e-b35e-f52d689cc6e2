const express = require('express');
const router = express.Router();
const videoController = require('../controllers/videoController');

// POST /api/video/generate - Generate video for a shot
router.post('/generate', videoController.generateVideo);

// POST /api/video/generate-batch - Generate videos for multiple shots
router.post('/generate-batch', videoController.generateBatchVideo);

// POST /api/video/compose - Compose final video from shots
router.post('/compose', videoController.composeVideo);

// GET /api/video/status/:taskId - Get video generation status
router.get('/status/:taskId', videoController.getVideoStatus);

// POST /api/video/export - Export final video
router.post('/export', videoController.exportVideo);

module.exports = router;
