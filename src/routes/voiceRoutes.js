const express = require('express');
const router = express.Router();
const voiceController = require('../controllers/voiceController');

// POST /api/voice/generate - Generate voice for shots
router.post('/generate', voiceController.generateVoice);

// POST /api/voice/generate-batch - Generate voice for multiple shots
router.post('/generate-batch', voiceController.generateBatchVoice);

// GET /api/voice/preview/:voiceId - Get voice preview
router.get('/preview/:voiceId', voiceController.getVoicePreview);

// POST /api/voice/synthesize - Synthesize text to speech
router.post('/synthesize', voiceController.synthesizeText);

module.exports = router;
